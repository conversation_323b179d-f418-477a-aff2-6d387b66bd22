<?php

return [
    'families' => 'Families',
    'family' => 'Family',
    'create_family' => 'Create Your First Family',
    'create_new_family' => 'Create New Family',
    'edit_family' => 'Edit Family',
    'delete_family' => 'Delete Family',
    'family_details' => 'Family Details',
    'family_information' => 'Family Information',
    'your_families' => 'Families You Own',
    'member_families' => 'Families You\'re a Member Of',
    'no_families' => 'You haven\'t created any families yet.',
    'family_members' => 'Family Members',
    'family_member' => 'Family Member',
    'add_member' => 'Add Member',
    'add_family_member' => 'Add Family Member',
    'edit_family_member' => 'Edit Family Member',
    'no_members' => 'No members found.',
    'member_count' => 'Member Count',
    'members' => 'members',
    'member' => 'Member',
    'owner' => 'Owner',
    'you' => '(You)',
    'remove' => 'Remove',
    'delete_family_confirm' => 'Are you sure you want to delete this family?',
    'remove_family_confirm' => 'Are you sure you want to remove this family?',
    'remove_member_confirm' => 'Are you sure you want to remove this member from the family?',
    'relationship' => 'Relationship',
    'update_relationship' => 'Update Relationship',
    'select_relationship' => 'Select Relationship',
    'relationship_categories' => [
        'primary' => 'Primary Relationships',
        'extended' => 'Extended Family',
        'non_family' => 'Non-Family',
        'generic' => 'Other Relationships',
    ],
    'relationship_types' => [
        // Primary relationships
        'spouse' => 'Spouse',
        'child' => 'Child',
        'parent' => 'Parent',
        'sibling' => 'Sibling',
        
        // Extended family relationships
        'grandparent' => 'Grandparent',
        'grandchild' => 'Grandchild',
        'aunt_uncle' => 'Aunt/Uncle',
        'niece_nephew' => 'Niece/Nephew',
        'cousin' => 'Cousin',
        'in_law' => 'In-law',
        'step_relation' => 'Step Relation',
        
        // Non-family relationships
        'friend' => 'Friend',
        'roommate' => 'Roommate',
        
        // Generic relationships
        'family' => 'Family Member',
        'other' => 'Other',
    ],
    'email_address' => 'Email Address',
    'member_email_hint' => 'Enter the email address of the user you want to add to your family.',
    'joined_on' => 'Joined On',
    'role' => 'Role',
    'back_to_families' => 'Back to Families',
    
    // Invitations
    'invitation' => 'Invitation',
    'invitations' => 'Invitations',
    'pending_invitations' => 'Pending Invitations',
    'invitation_to_join' => 'Invitation to Join',
    'invitation_subject' => 'Invitation to join :family family',
    'invitation_greeting' => 'Hello :name,',
    'invitation_intro' => ':inviter has invited you to join the ":family" family.',
    'accept_invitation' => 'Accept Invitation',
    'decline_invitation' => 'Decline Invitation',
    'invitation_expires' => 'This invitation will expire in 7 days.',
    'invitation_ignore' => 'If you don\'t want to join this family, you can safely ignore this email.',
    'invitation_expired' => 'This invitation has expired.',
    'invitation_expired_description' => 'The invitation link has expired. Please request a new invitation from the family owner.',
    'invitation_already_processed' => 'This invitation has already been processed.',
    'invitation_accepted' => 'Invitation accepted successfully! You are now a member of this family.',
    'invitation_declined' => 'Invitation declined successfully.',
    'invitation_sent_successfully' => 'Invitation sent successfully.',
    'member_added_successfully' => 'Member added successfully.',
    'no_pending_invitations' => 'You have no pending invitations.',
    'your_invitations' => 'Your Pending Invitations',
    'invitations_message' => 'Below are the invitations you have received to join families. You can accept or decline these invitations.',
    'family_name' => 'Family Name',
    'invited_by' => 'Invited By',
    'expires_at' => 'Expires On',
    'actions' => 'Actions',
    'view_invitation' => 'View Invitation',
    'join_family_invitation' => 'You have been invited to join a family',
    'invitation_description' => 'Please review the details below and choose whether to accept or decline this invitation.',
    'back_to_dashboard' => 'Back to Dashboard',
    'shared_accounts' => 'Shared Accounts',
    'no_shared_accounts' => 'No shared accounts found.',
    'account_name' => 'Account Name',
    'balance' => 'Balance',
    'permission' => 'Permission',
    'can_edit' => 'Can Edit',
    'view_only' => 'View Only',
    'share_account' => 'Share Account',
    'unshare' => 'Unshare',
    'unshare_confirm' => 'Are you sure you want to stop sharing this account with the family?',
    'created_on' => 'Created On',
    'name' => 'Name',
    'description' => 'Description',
]; 