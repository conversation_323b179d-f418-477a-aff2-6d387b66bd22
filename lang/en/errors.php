<?php

return [
    'error_occurred' => 'Error Occurred',
    'details' => 'Details',
    'try_again' => 'Try Again',
    'go_back' => 'Go Back',
    'go_to_dashboard' => 'Go to Dashboard',
    'page_not_found' => 'Page Not Found',
    'page_not_found_message' => 'Sorry, the page you are looking for could not be found.',
    'model_not_found' => 'No query results for model',
    'ajax_request_failed' => 'AJAX Request Failed',
    'refresh_page' => 'Refresh Page',
    'dismiss' => 'Dismiss',
    'db_error' => 'There was a problem with the database operation.',
    'validation_error' => 'The submitted form contains validation errors.',
    'permission_error' => 'You do not have permission to perform this action.',
    'record_not_found' => 'The requested record could not be found.',
    'page_not_found_error' => 'The requested page could not be found.',
    'session_expired' => 'Your session has expired. Please try again.',
    'general_error' => 'An unexpected error occurred.',
    'error_in_file' => 'Error in :file on line :line',
    'network_error' => 'Network error occurred',
]; 