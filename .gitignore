# Node modules directory
/node_modules
node_modules/
# Laravel specific build directories
/public/hot
/public/build
/public/modules/appbuilder/build
/public/storage

# Sensitive files
/storage/*.key
.env
.env.backup
.env.example
.env.staging
.env.production
.env.dev

# Ignore all .env backup files in the env_backups folder
env_backups/pre-release-*.env.backup

# PHPUnit cache
/.phpunit.cache

# Homestead specific files
Homestead.json
Homestead.yaml

# Log files
npm-debug.log
yarn-error.log

# IDE and editor specific directories and files
/.idea
/.vscode/
.vscode/*
/.vagrant

# Composer dependencies directory
/vendor/

# Lock files
package-lock.json

# Output files
/nohup.out

# API documentation
/storage/api-docs/api-docs.json

# Clockwork debugging tool files
/storage/clockwork/*.json
/storage/clockwork/index

# Backup files
live_code_backup.zip

# Debug and test data
allRoutes.txt
apiRoutes.txt
/fake_data
storage/dummyData/*
storage/dummyData/whatsapp_template_components_sample.json
storage/hot

# OS-specific files (macOS)
.DS_Store

# OS-specific files (Windows)
Thumbs.db
Desktop.ini

# Log files
*.log

# Temporary files
*.tmp
*.temp

# Backup files
*.bak
*.old

# Cache files
*.cache

# Ignore coverage reports
coverage/

# Ignore build artifacts
/build/

# Ignore compiled PHP files
*.phpunit.result.cache

# Ignore environment variable files
.env.*.local


allRoutes.txt
.scribe/.filehashes
.scribe/intro.md

