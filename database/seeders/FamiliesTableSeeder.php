<?php

namespace Database\Seeders;

use App\Models\Family;
use App\Models\User;
use Illuminate\Database\Seeder;

class FamiliesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get users to associate with families
        $users = User::all();

        // Create families with Arabic names and descriptions
        $arabicFamilies = [
            [
                'name' => 'عائلة محمد',
                'description' => 'عائلة محمد الكبيرة التي تضم الأجداد والأبناء والأحفاد',
                'owner_id' => $users[0]->id,
                'members' => [$users[1]->id, $users[2]->id]
            ],
            [
                'name' => 'عائلة أحمد',
                'description' => 'عائلة أحمد المكونة من الزوج والزوجة والأبناء',
                'owner_id' => $users[1]->id,
                'members' => [$users[3]->id]
            ],
            [
                'name' => 'شركاء السكن',
                'description' => 'مجموعة من الأصدقاء الذين يعيشون معًا ويديرون نفقاتهم المشتركة',
                'owner_id' => $users[3]->id,
                'members' => [$users[4]->id, $users[5]->id, $users[6]->id]
            ],
            [
                'name' => 'رحلة مع الأصدقاء',
                'description' => 'مجموعة للتخطيط وتنظيم نفقات الرحلة المشتركة مع الأصدقاء',
                'owner_id' => $users[4]->id,
                'members' => [$users[2]->id, $users[5]->id]
            ],
        ];

        foreach ($arabicFamilies as $familyData) {
            // Create the family
            $family = Family::create([
                'name' => $familyData['name'],
                'description' => $familyData['description'],
                'owner_id' => $familyData['owner_id'],
            ]);

            // Add owner as member
            $family->users()->attach($familyData['owner_id']);

            // Add other members
            foreach ($familyData['members'] as $memberId) {
                $family->users()->attach($memberId);
            }
        }
    }
}
