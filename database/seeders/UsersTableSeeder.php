<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UsersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create main test user
        User::create([
            'name' => 'أحمد محمد',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
        ]);

        // Create additional users with Arabic names
        $arabicUsers = [
            [
                'name' => 'فاطمة علي',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'محمد خالد',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'ليلى أحمد',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'عمر حسن',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'سارة يوسف',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'خالد إبراهيم',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'نور عبد الله',
                'email' => '<EMAIL>',
            ],
        ];

        foreach ($arabicUsers as $user) {
            User::create([
                'name' => $user['name'],
                'email' => $user['email'],
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ]);
        }
    }
}
