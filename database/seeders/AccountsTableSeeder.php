<?php

namespace Database\Seeders;

use App\Models\Account;
use App\Models\Family;
use App\Models\User;
use Illuminate\Database\Seeder;

class AccountsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get users and families
        $users = User::all();
        $families = Family::all();
        
        // Create accounts with Arabic names
        $arabicAccounts = [
            [
                'name' => 'حساب الراتب',
                'description' => 'حساب للراتب الشهري ونفقات المعيشة اليومية',
                'currency' => 'د.إ', // UAE Dirham
                'owner_id' => $users[0]->id,
                'shared_with_users' => [$users[1]->id],
                'shared_with_families' => [$families[0]->id],
                'permission' => 'edit'
            ],
            [
                'name' => 'حساب التوفير',
                'description' => 'حساب للادخار على المدى الطويل',
                'currency' => 'ر.س', // Saudi Riyal
                'owner_id' => $users[0]->id,
                'shared_with_users' => [],
                'shared_with_families' => [],
                'permission' => 'view'
            ],
            [
                'name' => 'نفقات المنزل',
                'description' => 'حساب لتتبع نفقات المنزل والفواتير الشهرية',
                'currency' => 'ج.م', // Egyptian Pound
                'owner_id' => $users[1]->id,
                'shared_with_users' => [$users[0]->id, $users[2]->id],
                'shared_with_families' => [$families[1]->id],
                'permission' => 'edit'
            ],
            [
                'name' => 'مصاريف الرحلة',
                'description' => 'حساب لتتبع نفقات الرحلة المشتركة مع الأصدقاء',
                'currency' => 'د.ت', // Tunisian Dinar
                'owner_id' => $users[4]->id,
                'shared_with_users' => [],
                'shared_with_families' => [$families[3]->id],
                'permission' => 'edit'
            ],
            [
                'name' => 'نفقات الشقة المشتركة',
                'description' => 'حساب لتتبع النفقات المشتركة للسكن مع الزملاء',
                'currency' => 'ج.م', // Egyptian Pound
                'owner_id' => $users[3]->id,
                'shared_with_users' => [],
                'shared_with_families' => [$families[2]->id],
                'permission' => 'edit'
            ],
            [
                'name' => 'الاستثمارات',
                'description' => 'حساب لتتبع الاستثمارات والأرباح',
                'currency' => 'ر.ق', // Qatari Riyal
                'owner_id' => $users[5]->id,
                'shared_with_users' => [$users[0]->id],
                'shared_with_families' => [],
                'permission' => 'view'
            ],
        ];

        foreach ($arabicAccounts as $accountData) {
            // Create the account
            $account = Account::create([
                'name' => $accountData['name'],
                'description' => $accountData['description'],
                'currency' => $accountData['currency'],
                'owner_id' => $accountData['owner_id'],
                'balance' => 0,
                'total_income' => 0,
                'total_expenses' => 0,
            ]);

            // Share with users
            foreach ($accountData['shared_with_users'] as $userId) {
                $account->users()->attach($userId, ['permission' => $accountData['permission']]);
            }

            // Share with families
            foreach ($accountData['shared_with_families'] as $familyId) {
                $account->families()->attach($familyId, ['permission' => $accountData['permission']]);
            }
        }
    }
}
