<?php

namespace Database\Seeders;

use App\Models\Account;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class TransactionsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get accounts and users
        $accounts = Account::all();
        $users = User::all();

        // Arabic categories for income and expenses
        $incomeCategories = [
            'راتب',
            'مكافأة',
            'عائد استثمار',
            'هدية',
            'بيع أغراض',
            'دخل إضافي',
            'تحويل مالي'
        ];

        $expenseCategories = [
            'طعام',
            'سكن',
            'فواتير',
            'تسوق',
            'مواصلات',
            'صحة',
            'ترفيه',
            'تعليم',
            'سفر',
            'متفرقات'
        ];

        // Arabic descriptions for transactions
        $incomeDescriptions = [
            'راتب شهر %s',
            'مكافأة مشروع %s',
            'أرباح استثمار في %s',
            'هدية من %s',
            'بيع %s',
            'دخل إضافي من %s',
            'تحويل من حساب %s'
        ];

        $expenseDescriptions = [
            'مشتريات من %s',
            'فاتورة %s',
            'مطعم %s',
            'تسوق في %s',
            'وقود للسيارة',
            'زيارة طبيب %s',
            'سينما وترفيه',
            'مصاريف دراسية',
            'تذاكر سفر إلى %s',
            'صيانة %s',
            'شراء %s'
        ];

        // Arabic months
        $arabicMonths = [
            'يناير',
            'فبراير',
            'مارس',
            'أبريل',
            'مايو',
            'يونيو',
            'يوليو',
            'أغسطس',
            'سبتمبر',
            'أكتوبر',
            'نوفمبر',
            'ديسمبر'
        ];

        // Arabic items or places
        $arabicItems = [
            'كارفور',
            'لولو هايبر ماركت',
            'محلات العائلة',
            'أمازون',
            'سوق دبي',
            'محلات الأنوار',
            'مكتبة جرير',
            'متجر الإلكترونيات',
            'سوق العصر',
            'المراعي',
            'العثيم',
            'بندة'
        ];

        // Create random transactions for each account
        foreach ($accounts as $account) {
            // Create 15-25 transactions per account
            $transactionsCount = rand(15, 25);
            
            // Get account owner
            $owner = $account->owner;
            
            // Get account users who can edit
            $sharedUsers = $account->users()->wherePivot('permission', 'edit')->get();
            $allUsers = $sharedUsers->push($owner);
            
            // Generate transactions
            for ($i = 0; $i < $transactionsCount; $i++) {
                // Random date in the last 3 months
                $date = Carbon::now()->subDays(rand(1, 90));
                
                // Random user who created the transaction
                $user = $allUsers[rand(0, count($allUsers) - 1)];
                
                // Random type (40% income, 60% expense)
                $type = rand(1, 100) <= 40 ? 'income' : 'expense';
                
                // Random category based on type
                if ($type === 'income') {
                    $category = $incomeCategories[rand(0, count($incomeCategories) - 1)];
                    $descriptionTemplate = $incomeDescriptions[rand(0, count($incomeDescriptions) - 1)];
                    
                    // Format description
                    if (strpos($descriptionTemplate, '%s') !== false) {
                        if ($category === 'راتب') {
                            $description = sprintf($descriptionTemplate, $arabicMonths[$date->month - 1]);
                        } else {
                            $description = sprintf($descriptionTemplate, $arabicItems[rand(0, count($arabicItems) - 1)]);
                        }
                    } else {
                        $description = $descriptionTemplate;
                    }
                    
                    // Random amount for income (1000-10000)
                    $amount = rand(1000, 10000);
                } else {
                    $category = $expenseCategories[rand(0, count($expenseCategories) - 1)];
                    $descriptionTemplate = $expenseDescriptions[rand(0, count($expenseDescriptions) - 1)];
                    
                    // Format description
                    if (strpos($descriptionTemplate, '%s') !== false) {
                        $description = sprintf($descriptionTemplate, $arabicItems[rand(0, count($arabicItems) - 1)]);
                    } else {
                        $description = $descriptionTemplate;
                    }
                    
                    // Random amount for expense (50-2000)
                    $amount = rand(50, 2000);
                }
                
                // Create transaction
                Transaction::create([
                    'account_id' => $account->id,
                    'user_id' => $user->id,
                    'type' => $type,
                    'amount' => $amount,
                    'description' => $description,
                    'category' => $category,
                    'transaction_date' => $date,
                ]);
            }
            
            // Recalculate account balance
            $account->recalculateBalance();
        }
    }
}
