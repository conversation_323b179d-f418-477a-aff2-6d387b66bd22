<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('allow_search_by_mobile')->default(false)->after('mobile_verified_at');
            $table->enum('family_invitation_mode', ['anyone', 'invite_only'])->default('invite_only')->after('allow_search_by_mobile');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('allow_search_by_mobile');
            $table->dropColumn('family_invitation_mode');
        });
    }
};
