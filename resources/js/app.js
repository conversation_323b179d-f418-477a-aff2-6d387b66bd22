import './bootstrap';

import Alpine from 'alpinejs';

window.Alpine = Alpine;

Alpine.start();

// Global error handling for AJAX requests
document.addEventListener('DOMContentLoaded', function() {
    // Load translations needed for JS
    window.translations = window.translations || {};
    
    // Try to get translations from the page
    const translationElements = document.querySelectorAll('[data-js-translations]');
    if (translationElements.length > 0) {
        try {
            window.translations = JSON.parse(translationElements[0].textContent);
        } catch (e) {
            console.error('Failed to parse translations');
        }
    }
    
    // Helper function for translations
    window.__ = function(key, replacements = {}) {
        let translation = key.split('.').reduce((obj, i) => obj && obj[i], window.translations);
        
        if (!translation) return key;
        
        // Replace any :placeholders with the provided replacements
        for (const [placeholder, value] of Object.entries(replacements)) {
            translation = translation.replace(`:${placeholder}`, value);
        }
        
        return translation;
    };
    
    // Add a global AJAX error handler
    window.handleAjaxError = function(xhr, textStatus, error) {
        // Create error recovery modal dynamically
        const errorMessage = xhr.responseJSON?.message || window.__('errors.general_error');
        const errorContext = xhr.responseJSON?.context || window.__('errors.refresh_page');
        
        // Create modal element
        const modal = document.createElement('div');
        modal.id = 'ajax-error-recovery-alert';
        modal.className = 'fixed inset-0 flex items-center justify-center z-50';
        modal.setAttribute('role', 'alert');
        
        // Set modal HTML content
        modal.innerHTML = `
            <div class="fixed inset-0 bg-gray-900 bg-opacity-50"></div>
            <div class="bg-white rounded-lg shadow-xl overflow-hidden max-w-md w-full z-10">
                <div class="bg-red-600 px-4 py-3 text-white flex justify-between items-center">
                    <div class="flex items-center">
                        <svg class="h-6 w-6 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                        <h3 class="text-lg font-medium">${window.__('errors.ajax_request_failed')}</h3>
                    </div>
                    <button onclick="document.getElementById('ajax-error-recovery-alert').remove()" class="text-white hover:text-gray-200">
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <div class="p-5">
                    <p class="text-gray-700 mb-4">${errorMessage}</p>
                    
                    <div class="bg-gray-100 p-3 rounded mb-4 text-sm">
                        <p class="font-semibold mb-1">${window.__('errors.details')}:</p>
                        <p>${errorContext}</p>
                    </div>
                    
                    <div class="flex flex-col space-y-3 mt-5">
                        <button onclick="window.location.reload()" class="inline-flex justify-center items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            ${window.__('errors.refresh_page')}
                        </button>
                        
                        <button onclick="document.getElementById('ajax-error-recovery-alert').remove()" class="inline-flex justify-center items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 6l12 12M6 18L18 6" />
                            </svg>
                            ${window.__('errors.dismiss')}
                        </button>
                        
                        <a href="/" class="inline-flex justify-center items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                            </svg>
                            ${window.__('errors.go_to_dashboard')}
                        </a>
                    </div>
                </div>
            </div>
        `;
        
        // Add to document
        document.body.appendChild(modal);
    };
    
    // Override fetch to add global error handling
    const originalFetch = window.fetch;
    window.fetch = function() {
        return originalFetch.apply(this, arguments)
            .then(response => {
                if (!response.ok) {
                    response.json().then(data => {
                        window.handleAjaxError({ 
                            responseJSON: { 
                                message: data.message || response.statusText,
                                context: data.context || `${window.__('errors.error')} ${response.status}`
                            }
                        });
                    }).catch(() => {
                        window.handleAjaxError({ 
                            responseJSON: { 
                                message: response.statusText,
                                context: `${window.__('errors.error')} ${response.status}`
                            }
                        });
                    });
                }
                return response;
            })
            .catch(error => {
                window.handleAjaxError({ 
                    responseJSON: { 
                        message: window.__('errors.network_error'),
                        context: error.message
                    }
                });
                throw error;
            });
    };
    
    // If jQuery is used in the app, add error handling to jQuery AJAX
    if (window.jQuery) {
        $(document).ajaxError(function(event, xhr, settings, error) {
            window.handleAjaxError(xhr, error);
        });
    }
});
