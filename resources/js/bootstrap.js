import axios from 'axios';
window.axios = axios;

window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

// Add global error handler for Axios
window.axios.interceptors.response.use(
    response => response,
    error => {
        // Extract error data
        const errorMessage = error.response?.data?.message || 'An unexpected error occurred';
        const errorStatus = error.response?.status || 500;
        const errorContext = error.response?.data?.context || `Status: ${errorStatus}`;
        
        // Use the global error handler if available, or create a simple alert
        if (window.handleAjaxError) {
            window.handleAjaxError({
                responseJSON: {
                    message: errorMessage,
                    context: errorContext
                }
            });
        } else {
            // Fallback error handling if the main handler isn't loaded yet
            console.error('Axios Error:', errorMessage);
            alert(`Error: ${errorMessage}`);
        }
        
        // Continue throwing the error for additional handling
        return Promise.reject(error);
    }
);
