@props(['user'])

<section class="space-y-6">
    <header>
        <h2 class="text-lg font-medium text-gray-900">
            {{ __('users.delete_account') }}
        </h2>

        <p class="mt-1 text-sm text-gray-600">
            {{ __('users.once_deleted') }}
        </p>
    </header>

    <x-button
        variant="danger"
        x-data=""
        x-on:click.prevent="$dispatch('open-modal', 'confirm-user-deletion')"
    >{{ __('users.delete_account') }}</x-button>

    <x-modal name="confirm-user-deletion" :show="$errors->userDeletion->isNotEmpty()" focusable>
        <form method="post" action="{{ route('profile.destroy') }}" class="p-6">
            @csrf
            @method('delete')

            <h2 class="text-lg font-medium text-gray-900">
                {{ __('users.delete_confirmation') }}
            </h2>

            <p class="mt-1 text-sm text-gray-600">
                {{ __('users.delete_confirmation_detail') }}
            </p>

            <div class="mt-6">
                <x-forms.inputs.fields.dynamic-input
                    type="password"
                    name="password"
                    label="{{ __('users.password') }}"
                    class="mt-1 block w-3/4"
                    placeholder="{{ __('users.password') }}"
                    :error="$errors->userDeletion->get('password')"
                />
            </div>

            <div class="mt-6 flex justify-end">
                <x-button variant="secondary" x-on:click="$dispatch('close')">
                    {{ __('common.cancel') }}
                </x-button>

                <x-button variant="danger" class="ms-3">
                    {{ __('users.delete_account') }}
                </x-button>
            </div>
        </form>
    </x-modal>
</section>
