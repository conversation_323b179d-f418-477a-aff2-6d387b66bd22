<section>
    <header>
        <h2 class="text-lg font-medium text-gray-900">
            {{ __('users.update_password') }}
        </h2>

        <p class="mt-1 text-sm text-gray-600">
            {{ __('users.ensure_secure_password') }}
        </p>
    </header>

    <form method="post" action="{{ route('password.update') }}" class="mt-6 space-y-6">
        @csrf
        @method('put')

        <div>
            <x-forms.inputs.fields.dynamic-input
                name="current_password"
                label="{{ __('users.current_password') }}"
                type="password"
                autocomplete="current-password"
            />
        </div>

        <div>
            <x-forms.inputs.fields.dynamic-input
                name="password"
                label="{{ __('users.new_password') }}"
                type="password"
                autocomplete="new-password"
            />
        </div>

        <div>
            <x-forms.inputs.fields.dynamic-input
                name="password_confirmation"
                label="{{ __('users.confirm_password') }}"
                type="password"
                autocomplete="new-password"
            />
        </div>

        <div class="flex items-center gap-4">
            <x-button variant="primary">{{ __('users.save') }}</x-button>

            @if (session('status') === 'password-updated')
                <p
                    x-data="{ show: true }"
                    x-show="show"
                    x-transition
                    x-init="setTimeout(() => show = false, 2000)"
                    class="text-sm text-gray-600"
                >{{ __('users.saved') }}</p>
            @endif
        </div>
    </form>
</section>
