<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Account Summary Section -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h3 class="text-lg font-semibold mb-4">{{ __('Your Accounts') }}</h3>

                    @if($ownedAccounts->isEmpty() && $sharedAccounts->isEmpty())
                        <div class="text-center py-4">
                            <p class="text-gray-500">{{ __('You don\'t have any accounts yet.') }}</p>
                            <a href="{{ route('accounts.create') }}" class="mt-2 inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-800 focus:outline-none focus:border-blue-800 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150">
                                {{ __('Create Account') }}
                            </a>
                        </div>
                    @else
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($ownedAccounts as $account)
                                <div class="border rounded-lg p-4 bg-gray-50 hover:bg-gray-100 transition">
                                    <a href="{{ route('accounts.show', $account) }}" class="block">
                                        <h4 class="font-semibold text-blue-700">{{ $account->name }}</h4>
                                        <div class="mt-2 text-sm text-gray-600">{{ __('Balance:') }}
                                            <x-currency-display :currencyCode="$account->currency" :amount="$account->balance" />
                                        </div>

                                        <div class="mt-1 text-sm text-gray-600">{{ $account->transactions_count }} {{ __('transactions.transactions') }}</div>
                                        <div class="mt-2 text-xs text-gray-500">{{ __('Your account') }}</div>
                                    </a>
                                </div>
                            @endforeach

                            @foreach($sharedAccounts as $account)
                                <div class="border rounded-lg p-4 bg-gray-50 hover:bg-gray-100 transition">
                                    <a href="{{ route('accounts.show', $account) }}" class="block">
                                        <h4 class="font-semibold text-blue-700">{{ $account->name }}</h4>
                                        <div class="mt-2 text-sm text-gray-600">{{ __('Balance:') }}
                                            <x-currency-display :currencyCode="$account->currency" :amount="$account->balance" />
                                        </div>
                                        <div class="mt-1 text-sm text-gray-600">{{ $account->transactions_count }} {{ __('transactions.transactions') }}</div>
                                        <div class="mt-2 text-xs text-gray-500">{{ __('Shared with you') }}</div>
                                    </a>
                                </div>
                            @endforeach
                        </div>

                        <div class="mt-4 text-center">
                            <a href="{{ route('accounts.create') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-800 focus:outline-none focus:border-blue-800 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150">
                                {{ __('Create New Account') }}
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Recent Transactions Section -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h3 class="text-lg font-semibold mb-4">{{ __('Recent Transactions') }}</h3>

                    @if($recentTransactions->isEmpty())
                        <div class="text-center py-4">
                            <p class="text-gray-500">{{ __('No recent transactions found.') }}</p>
                        </div>
                    @else
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Date') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Account') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Description') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Amount') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Created By') }}</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($recentTransactions as $transaction)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ $transaction->transaction_date->format('M d, Y') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                <a href="{{ route('accounts.show', $transaction->account) }}" class="text-blue-600 hover:text-blue-900">
                                                    {{ $transaction->account->name }}
                                                </a>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ $transaction->description }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium {{ $transaction->type === 'income' ? 'text-green-600' : 'text-red-600' }}">
                                                {{ $transaction->type === 'income' ? '+' : '-' }}
                                                <x-currency-display :currencyCode="$transaction->account->currency" :amount="$transaction->amount" :showFlag="false" />
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ $transaction->user->name }}
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
