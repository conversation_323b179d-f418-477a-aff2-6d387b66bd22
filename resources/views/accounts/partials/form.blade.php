@props([
    'action',
    'method' => 'POST',
    'account' => null,
    'buttonText' => __('Save'),
    'arabicCurrencies',
    'allCurrencies'
])

<form action="{{ $action }}" method="POST">
    @csrf
    @if(strtoupper($method) !== 'POST')
        @method($method)
    @endif

    <div class="mb-4">
        <x-input-label for="name" :value="__('Account Name')" />
        <x-text-input id="name" class="block mt-1 w-full" type="text" name="name" :value="old('name', $account?->name)" required autofocus />
        <x-input-error :messages="$errors->get('name')" class="mt-2" />
    </div>

    <div class="mb-4">
        <x-input-label for="description" :value="__('Description (optional)')" />
        <textarea id="description" name="description" class="block mt-1 w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm">{{ old('description', $account?->description) }}</textarea>
        <x-input-error :messages="$errors->get('description')" class="mt-2" />
    </div>

    <div class="mb-4">
        <x-input-label for="currency" :value="__('Currency')" />
        <select id="currency" name="currency" class="block mt-1 w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm">
            <optgroup label="{{ __('Arabic Countries Currencies') }}">
                @foreach($arabicCurrencies as $code => $currency)
                    <option value="{{ $code }}" {{ old('currency', $account?->currency) == $code ? 'selected' : '' }}>
                        {{ $currency['flag'] }} {{ $code }} - {{ $currency['name'] }} ({{ $currency['symbol'] }})
                    </option>
                @endforeach
            </optgroup>
            <optgroup label="{{ __('Other Common Currencies') }}">
                @foreach($allCurrencies as $code => $currency)
                    @if(!isset($arabicCurrencies[$code]))
                        <option value="{{ $code }}" {{ old('currency', $account?->currency) == $code ? 'selected' : '' }}>
                            {{ $currency['flag'] }} {{ $code }} - {{ $currency['name'] }} ({{ $currency['symbol'] }})
                        </option>
                    @endif
                @endforeach
            </optgroup>
        </select>
        <x-input-error :messages="$errors->get('currency')" class="mt-2" />
    </div>

    <div class="flex items-center justify-end mt-4">
        <a href="{{ isset($account) ? route('accounts.show', $account) : route('accounts.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-300 active:bg-gray-300 focus:outline-none focus:border-gray-900 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150 mr-2">
            {{ __('Cancel') }}
        </a>
        <x-button>
            {{ $buttonText }}
        </x-button>
    </div>
</form> 