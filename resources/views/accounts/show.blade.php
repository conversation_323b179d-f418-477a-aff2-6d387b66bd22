<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <x-forms.buttons.button
                    :href="route('accounts.index')" 
                    variant="secondary" 
                    class="flex items-center shadow-sm hover:shadow"
                >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                </x-forms.buttons.button>
                
                <h2 class="font-semibold text-xl text-gray-800 leading-tight ml-1">
                    {{ $account->name }}
                </h2>
            </div>
            <div class="flex space-x-2">
                @can('update', $account)
                    <x-forms.buttons.button :href="route('accounts.transactions.create', $account)" variant="primary" class="flex items-center">
                        <x-icon name="add-transaction" class="sm:mr-1" />
                        <span class="hidden sm:inline">{{ __('Add Transaction') }}</span>
                    </x-forms.buttons.button>
                @endcan
                
                @if(auth()->id() === $account->owner_id)
                    <x-forms.buttons.button :href="route('accounts.edit', $account)" variant="warning" class="flex items-center">
                        <x-icon name="edit" class="sm:mr-1" />
                        <span class="hidden sm:inline">{{ __('Edit Account') }}</span>
                    </x-forms.buttons.button>
                @endif
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if (session('success'))
                <x-alert type="success" dismissible>
                    {{ session('success') }}
                </x-alert>
            @endif

            <!-- Account Summary -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="border rounded-lg p-4 bg-blue-50">
                            <div class="text-sm text-gray-600">{{ __('Owner') }}</div>
                            <div class="font-semibold">{{ $account->owner->name }}</div>
                        </div>
                        
                        <div class="border rounded-lg p-4 bg-green-50">
                            <div class="text-sm text-gray-600">{{ __('Total Income') }}</div>
                            <div class="font-semibold text-green-600">
                                <x-currency-display :currencyCode="$account->currency" :amount="$account->total_income" />
                            </div>
                        </div>
                        
                        <div class="border rounded-lg p-4 bg-red-50">
                            <div class="text-sm text-gray-600">{{ __('Total Expenses') }}</div>
                            <div class="font-semibold text-red-600">
                                <x-currency-display :currencyCode="$account->currency" :amount="$account->total_expenses" />
                            </div>
                        </div>
                        
                        <div class="border rounded-lg p-4 {{ $account->balance >= 0 ? 'bg-green-50' : 'bg-red-50' }}">
                            <div class="text-sm text-gray-600">{{ __('Current Balance') }}</div>
                            <div class="font-semibold {{ $account->balance >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                <x-currency-display :currencyCode="$account->currency" :amount="$account->balance" />
                            </div>
                        </div>
                    </div>
                    
                    @if($account->description)
                        <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                            <div class="text-sm text-gray-600 mb-1">{{ __('Description') }}</div>
                            <div>{{ $account->description }}</div>
                        </div>
                    @endif
                    
                    @if(auth()->id() === $account->owner_id)
                        <div class="mt-4 flex justify-end">
                            <x-actions>
                                <x-action-button 
                                    :href="route('reports.accounts.excel', $account)" 
                                    icon="excel" 
                                    color="green" 
                                    :tooltip="__('Export to Excel')" 
                                />
                                <x-action-button 
                                    :href="route('reports.accounts.pdf', $account)" 
                                    icon="pdf" 
                                    color="red" 
                                    :tooltip="__('Export to PDF')" 
                                />
                            </x-actions>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Transactions -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">{{ __('Transactions') }}</h3>
                        @can('update', $account)
                            <x-forms.buttons.button :href="route('accounts.transactions.create', $account)" variant="primary" class="flex items-center">
                                <x-icon name="add-transaction" class="sm:mr-1" />
                                <span class="hidden sm:inline">{{ __('Add Transaction') }}</span>
                            </x-forms.buttons.button>
                        @endcan
                    </div>
                    
                    @if($account->transactions->isEmpty())
                        <div class="text-center py-4">
                            <p class="text-gray-500 mb-4">{{ __('No transactions found for this account.') }}</p>
                            @can('update', $account)
                                <x-forms.buttons.button :href="route('accounts.transactions.create', $account)" variant="primary" class="flex items-center">
                                    <x-icon name="add-transaction" class="sm:mr-1" />
                                    <span class="hidden sm:inline">{{ __('Add Your First Transaction') }}</span>
                                </x-forms.buttons.button>
                            @endcan
                        </div>
                    @else
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Date') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Description') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Category') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Amount') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Created By') }}</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($account->transactions as $transaction)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ $transaction->transaction_date ? $transaction->transaction_date->format('M d, Y') : '-' }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <a href="{{ route('transactions.show', $transaction) }}" class="text-blue-600 hover:text-blue-900">
                                                    {{ $transaction->description }}
                                                </a>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ $transaction->category ?? '-' }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium {{ $transaction->type === 'income' ? 'text-green-600' : 'text-red-600' }}">
                                                {{ $transaction->type === 'income' ? '+' : '-' }}
                                                <x-currency-display :currencyCode="$account->currency" :amount="$transaction->amount" :showFlag="false" />
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ $transaction->user->name }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <x-actions>
                                                    <x-action-button 
                                                        :href="route('transactions.show', $transaction)" 
                                                        icon="view" 
                                                        color="blue" 
                                                        :tooltip="__('View')" 
                                                    />
                                                    
                                                    @can('update', $transaction)
                                                        <x-action-button 
                                                            :href="route('transactions.edit', $transaction)" 
                                                            icon="edit" 
                                                            color="yellow" 
                                                            :tooltip="__('Edit')" 
                                                        />
                                                    @endcan
                                                    
                                                    @can('delete', $transaction)
                                                        <x-action-button 
                                                            :action="route('transactions.destroy', $transaction)" 
                                                            method="DELETE" 
                                                            icon="trash" 
                                                            color="red" 
                                                            :tooltip="__('Delete')" 
                                                            onclick="return confirm('{{ __('Are you sure you want to delete this transaction?') }}')" 
                                                        />
                                                    @endcan
                                                </x-actions>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>

        </div>
    </div>
</x-app-layout>
