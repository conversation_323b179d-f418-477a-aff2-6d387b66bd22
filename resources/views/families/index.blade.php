<x-app-layout>
    <x-slot name="header">
        <x-page-header title="{{ __('families.families') }}" :backRoute="route('dashboard')">
            <x-forms.buttons.button :href="route('families.create')" variant="primary" class="flex items-center">
                <x-icon name="plus" class="sm:mr-1" />
                <span class="hidden sm:inline">{{ __('families.create_new_family') }}</span>
            </x-forms.buttons.button>
        </x-page-header>
    </x-slot>

    <x-page-content>
        @if (session('success'))
            <x-alert type="success" dismissible>
                {{ session('success') }}
            </x-alert>
        @endif

        <x-card :title="__('families.families_you_own')">
            @if($ownedFamilies->isEmpty())
                <div class="text-center py-6">
                    <p class="text-gray-500 mb-4">{{ __('families.no_families') }}</p>
                    <x-forms.buttons.button :href="route('families.create')" variant="primary" class="flex items-center">
                        <x-icon name="plus" class="mr-1" />
                        {{ __('families.create_family') }}
                    </x-forms.buttons.button>
                </div>
            @else
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('families.name') }}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('families.description') }}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('families.relationship') }}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('families.members') }}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('families.created_on') }}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('common.actions') }}</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($ownedFamilies as $family)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ $family->name }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ Str::limit($family->description, 50) ?? 'N/A' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ $family->relationship ?? 'N/A' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ $family->users->count() }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ $family->created_at->format('M d, Y') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <x-actions>
                                            <x-action-button 
                                                :href="route('families.show', $family)" 
                                                icon="view" 
                                                color="blue" 
                                                :tooltip="__('common.view')" 
                                            />
                                            
                                            <x-action-button 
                                                :href="route('families.edit', $family)" 
                                                icon="edit" 
                                                color="yellow" 
                                                :tooltip="__('common.edit')" 
                                            />
                                            
                                            <x-action-button 
                                                :action="route('families.destroy', $family)" 
                                                method="DELETE" 
                                                icon="trash" 
                                                color="red" 
                                                :tooltip="__('families.remove')" 
                                                onclick="return confirm('{{ __('families.remove_family_confirm') }}')" 
                                            />
                                        </x-actions>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @endif
        </x-card>

        @if(!$memberFamilies->isEmpty())
            <x-card :title="__('families.families_member_of')">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('families.name') }}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('families.description') }}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('families.relationship') }}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('families.members') }}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('families.created_on') }}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{{ __('common.actions') }}</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($memberFamilies as $family)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        {{ $family->name }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ Str::limit($family->description, 50) ?? 'N/A' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ $family->relationship ?? 'N/A' }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ $family->users->count() }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ $family->created_at->format('M d, Y') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <x-actions>
                                            <x-action-button 
                                                :href="route('families.show', $family)" 
                                                icon="view" 
                                                color="blue" 
                                                :tooltip="__('common.view')" 
                                            />
                                        </x-actions>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </x-card>
        @endif

        <div class="mt-6 flex space-x-4">
            <x-forms.buttons.button :href="route('dashboard')" variant="secondary" class="flex items-center shadow-sm hover:shadow transition-all">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                {{ __('common.back_to_dashboard') }}
            </x-forms.buttons.button>
        </div>
    </x-page-content>
</x-app-layout>
