<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Family Invitation') }}
        </h2>
    </x-slot>

    <x-page-content>
        <x-card>
            <div class="text-center mb-6">
                <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ __('families.join_family_invitation') }}</h3>
                <p class="text-gray-600">{{ __('families.invitation_description') }}</p>
            </div>

            <div class="mb-6 bg-gray-50 p-6 rounded-lg">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500">{{ __('families.family_name') }}</h4>
                        <p class="mt-1 text-lg font-medium text-gray-900">{{ $invitation->family->name }}</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500">{{ __('families.invited_by') }}</h4>
                        <p class="mt-1 text-lg font-medium text-gray-900">{{ $invitation->inviter->name }}</p>
                    </div>
                    @if($invitation->relationship)
                    <div>
                        <h4 class="text-sm font-medium text-gray-500">{{ __('families.relationship') }}</h4>
                        <p class="mt-1 text-lg font-medium text-gray-900">{{ $invitation->relationship }}</p>
                    </div>
                    @endif
                    <div>
                        <h4 class="text-sm font-medium text-gray-500">{{ __('families.expires_at') }}</h4>
                        <p class="mt-1 text-lg font-medium text-gray-900">{{ $invitation->expires_at->format('F d, Y') }}</p>
                    </div>
                </div>
            </div>

            <div class="flex justify-center space-x-4">
                <form action="{{ route('families.invitations.decline', $invitation->token) }}" method="POST">
                    @csrf
                    <button type="submit" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-800 focus:outline-none focus:border-gray-800 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
                        {{ __('families.decline_invitation') }}
                    </button>
                </form>
                
                <form action="{{ route('families.invitations.accept', $invitation->token) }}" method="POST">
                    @csrf
                    <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-800 focus:outline-none focus:border-blue-800 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150">
                        {{ __('families.accept_invitation') }}
                    </button>
                </form>
            </div>
        </x-card>
    </x-page-content>
</x-app-layout> 