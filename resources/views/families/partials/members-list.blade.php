@props(['family'])

<x-card>
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">{{ __('families.family_members') }}</h3>
        @if($family->isOwner())
            <x-action-button 
                :href="route('families.add-member.form', $family)" 
                icon="user-plus" 
                color="blue"
            >
                {{ __('families.add_member') }}
            </x-action-button>
        @endif
    </div>
    
    <div class="mt-2">
        @if($family->users->isEmpty())
            <x-empty-state message="{{ __('families.no_members') }}" />
        @else
            <div class="overflow-x-auto">
                <x-table>
                    <x-table.head>
                        <x-table.tr>
                            <x-table.th>{{ __('families.name') }}</x-table.th>
                            <x-table.th>{{ __('families.email_address') }}</x-table.th>
                            <x-table.th>{{ __('families.role') }}</x-table.th>
                            <x-table.th>{{ __('families.joined_on') }}</x-table.th>
                            @if($family->isOwner())
                                <x-table.th>{{ __('common.actions') }}</x-table.th>
                            @endif
                        </x-table.tr>
                    </x-table.head>
                    
                    <x-table.body>
                        @foreach($family->users as $user)
                            <x-table.tr>
                                <x-table.td class="font-medium">{{ $user->name }}</x-table.td>
                                <x-table.td>{{ $user->email }}</x-table.td>
                                <x-table.td>
                                    @if($user->id === $family->owner_id)
                                        <x-badge color="green">{{ __('families.owner') }}</x-badge>
                                    @else
                                        <x-badge color="blue">{{ __('families.member') }}</x-badge>
                                    @endif
                                </x-table.td>
                                <x-table.td>{{ $user->pivot->created_at->format('M d, Y') }}</x-table.td>
                                
                                @if($family->isOwner() && $user->id !== auth()->id())
                                    <x-table.td>
                                        <form action="{{ route('families.remove-member', ['family' => $family->id, 'user' => $user->id]) }}" method="POST" class="inline">
                                            @csrf
                                            @method('DELETE')
                                            <x-forms.buttons.button
                                                variant="danger" 
                                                size="xs"
                                                icon="user-remove"
                                                onclick="return confirm('{{ __('families.remove_member_confirm') }}')"
                                            >
                                                {{ __('families.remove') }}
                                            </x-forms.buttons.button>
                                        </form>
                                    </x-table.td>
                                @elseif($family->isOwner())
                                    <x-table.td>
                                        <span class="text-gray-400">{{ __('families.you') }}</span>
                                    </x-table.td>
                                @endif
                            </x-table.tr>
                        @endforeach
                    </x-table.body>
                </x-table>
            </div>
        @endif
    </div>
</x-card> 