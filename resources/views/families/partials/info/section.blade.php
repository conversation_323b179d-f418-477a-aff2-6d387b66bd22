@props(['family'])

<x-card :title="__('families.family_information')">
    <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-6">

        <x-pages.owner :modelInstance="$family" />

        @foreach(['name','description'] as $field)
            @include('families.partials.info.row', ['family' => $family,'field' => $field])
        @endforeach

        <div>
            <h4 class="text-sm font-medium text-gray-500">{{ __('families.member_count') }}</h4>
            <p class="mt-1 text-gray-900">{{ $family->users->count() }} {{ __('families.members') }}</p>
        </div>

        <x-pages.create-and-update-info :modelInstance="$family" />
    </div>
</x-card>
