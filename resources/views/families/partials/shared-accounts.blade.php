@props(['family'])

<x-card>
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">{{ __('families.shared_accounts') }}</h3>
        @if($family->isOwner())
            <x-action-button 
                :href="route('families.share-account.form', $family)" 
                icon="share" 
                color="green"
            >
                {{ __('families.share_account') }}
            </x-action-button>
        @endif
    </div>
    
    <div class="mt-2">
        @if($family->accounts->isEmpty())
            <x-empty-state message="{{ __('families.no_shared_accounts') }}" />
        @else
            <div class="overflow-x-auto">
                <x-table>
                    <x-table.head>
                        <x-table.tr>
                            <x-table.th>{{ __('families.account_name') }}</x-table.th>
                            <x-table.th>{{ __('families.balance') }}</x-table.th>
                            <x-table.th>{{ __('families.owner') }}</x-table.th>
                            <x-table.th>{{ __('families.permission') }}</x-table.th>
                            @if($family->isOwner())
                                <x-table.th>{{ __('common.actions') }}</x-table.th>
                            @endif
                        </x-table.tr>
                    </x-table.head>
                    
                    <x-table.body>
                        @foreach($family->accounts as $account)
                            <x-table.tr>
                                <x-table.td class="font-medium">
                                    <a href="{{ route('accounts.show', $account) }}" class="text-blue-600 hover:text-blue-800">
                                        {{ $account->name }}
                                    </a>
                                </x-table.td>
                                <x-table.td>
                                    <x-currency-display :currencyCode="$account->currency" :amount="$account->balance" />
                                </x-table.td>
                                <x-table.td>{{ $account->owner->name }}</x-table.td>
                                <x-table.td>
                                    @if($account->pivot->permission === 'edit')
                                        <x-badge color="blue">{{ __('families.can_edit') }}</x-badge>
                                    @else
                                        <x-badge color="gray">{{ __('families.view_only') }}</x-badge>
                                    @endif
                                </x-table.td>
                                
                                @if($family->isOwner())
                                    <x-table.td>
                                        <form action="{{ route('families.unshare-account', ['family' => $family->id, 'account' => $account->id]) }}" method="POST" class="inline">
                                            @csrf
                                            @method('DELETE')
                                            <x-forms.buttons.button
                                                variant="danger" 
                                                size="xs"
                                                icon="x-circle"
                                                onclick="return confirm('{{ __('families.unshare_confirm') }}')"
                                            >
                                                {{ __('families.unshare') }}
                                            </x-forms.buttons.button>
                                        </form>
                                    </x-table.td>
                                @endif
                            </x-table.tr>
                        @endforeach
                    </x-table.body>
                </x-table>
            </div>
        @endif
    </div>
</x-card> 