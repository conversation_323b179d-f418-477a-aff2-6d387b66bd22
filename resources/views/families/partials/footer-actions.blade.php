@props(['family'])

<div class="mt-6 flex space-x-4">
    <x-forms.buttons.button
        :href="route('families.index')" 
        variant="secondary"
        class="flex items-center shadow-sm hover:shadow transition-all"
    >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        {{ __('families.back_to_families') }}
    </x-forms.buttons.button>

    @if($family->isOwner())
        <form action="{{ route('families.destroy', $family) }}" method="POST" class="inline">
            @csrf
            @method('DELETE')
            <x-forms.buttons.button
                variant="danger" 
                class="flex items-center shadow-sm hover:shadow transition-all"
                onclick="return confirm('{{ __('families.delete_family_confirm') }}')"
            >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                {{ __('families.delete_family') }}
            </x-forms.buttons.button>
        </form>
    @endif
</div> 