<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" class="h-full bg-gray-50">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Laravel') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600,700&display=swap" rel="stylesheet" />

        <!-- JavaScript Translations -->
        <script type="application/json" id="js-translations" data-js-translations>
            {
                "common": @json(__('common')),
                "errors": @json(__('errors')),
                "accounts": @json(__('accounts')),
                "transactions": @json(__('transactions')),
                "families": @json(__('families')),
                "auth": @json(__('auth'))
            }
        </script>

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
        
        @if(app()->getLocale() === 'ar')
        <style>
            /* RTL specific styles */
            body {
                font-family: 'figtree', Tahoma, Arial, sans-serif;
                text-align: right;
            }
            .text-left {
                text-align: right !important;
            }
            .text-right {
                text-align: left !important;
            }
            .ml-4 {
                margin-right: 1rem !important;
                margin-left: 0 !important;
            }
            .mr-4 {
                margin-left: 1rem !important;
                margin-right: 0 !important;
            }
            .ms-2 {
                margin-right: 0.5rem !important;
                margin-left: 0 !important;
            }
            .mr-1 {
                margin-left: 0.25rem !important;
                margin-right: 0 !important;
            }
            .mr-3 {
                margin-left: 0.75rem !important;
                margin-right: 0 !important;
            }
            .pl-3 {
                padding-right: 0.75rem !important;
                padding-left: 0 !important;
            }
            .pl-12 {
                padding-right: 3rem !important;
                padding-left: 0 !important;
            }
        </style>
        @endif
    </head>
    <body class="font-sans antialiased text-gray-900 h-full">
        <div class="min-h-screen flex flex-col">
            @include('layouts.navigation')

            <!-- Page Heading -->
            @isset($header)
                <header class="bg-white shadow">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        {{ $header }}
                    </div>
                </header>
            @else
                <!-- Fallback header with navigation buttons for pages without custom headers -->
                <header class="bg-white shadow">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        <div class="flex justify-between items-center">
                            <x-navigation-buttons />
                        </div>
                    </div>
                </header>
            @endisset

            <!-- Page Content -->
            <main class="flex-1 py-8">
                {{ $slot }}
            </main>

            <!-- Footer -->
            <footer class="bg-white border-t border-gray-200 py-4">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center text-sm text-gray-500">
                        &copy; {{ date('Y') }} {{ config('app.name') }}. {{ __('common.all_rights_reserved') }}
                    </div>
                </div>
            </footer>

            <!-- Error Recovery Component (if error exists) -->
            @if(session('error'))
                <x-error-recovery-alert />
            @endif
        </div>
    </body>
</html>
