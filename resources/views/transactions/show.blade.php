<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <a href="{{ route('accounts.show', $transaction->account) }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-800 focus:outline-none focus:border-gray-800 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
                    {{ __('Back') }}
                </a>
                <a href="{{ route('dashboard') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-800 focus:outline-none focus:border-blue-800 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150">
                    {{ __('Home') }}
                </a>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    {{ __('Transaction Details') }}
                </h2>
            </div>
            <div class="flex space-x-2">
                @can('update', $transaction)
                    <a href="{{ route('transactions.edit', $transaction) }}" class="inline-flex items-center px-4 py-2 bg-yellow-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-yellow-700 active:bg-yellow-800 focus:outline-none focus:border-yellow-800 focus:ring ring-yellow-300 disabled:opacity-25 transition ease-in-out duration-150">
                        {{ __('Edit') }}
                    </a>
                @endcan
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if (session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('Transaction Information') }}</h3>
                            
                            <div class="mb-4">
                                <div class="text-sm text-gray-600">{{ __('Account') }}</div>
                                <div class="font-semibold">{{ $transaction->account->name }}</div>
                            </div>
                            
                            <div class="mb-4">
                                <div class="text-sm text-gray-600">{{ __('Description') }}</div>
                                <div class="font-semibold">{{ $transaction->description }}</div>
                            </div>
                            
                            <div class="mb-4">
                                <div class="text-sm text-gray-600">{{ __('Type') }}</div>
                                <div class="font-semibold {{ $transaction->type === 'income' ? 'text-green-600' : 'text-red-600' }}">
                                    {{ ucfirst($transaction->type) }}
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <div class="text-sm text-gray-600">{{ __('Amount') }}</div>
                                <div class="font-semibold {{ $transaction->type === 'income' ? 'text-green-600' : 'text-red-600' }}">
                                    {{ $transaction->account->currency }} {{ number_format($transaction->amount, 2) }}
                                </div>
                            </div>
                            
                            @if($transaction->category)
                                <div class="mb-4">
                                    <div class="text-sm text-gray-600">{{ __('Category') }}</div>
                                    <div class="font-semibold">{{ $transaction->category }}</div>
                                </div>
                            @endif
                        </div>
                        
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('Additional Information') }}</h3>
                            
                            <div class="mb-4">
                                <div class="text-sm text-gray-600">{{ __('Transaction Date') }}</div>
                                <div class="font-semibold">{{ $transaction->transaction_date->format('F d, Y') }}</div>
                            </div>
                            
                            <div class="mb-4">
                                <div class="text-sm text-gray-600">{{ __('Created By') }}</div>
                                <div class="font-semibold">{{ $transaction->user->name }}</div>
                            </div>
                            
                            <x-pages.create-and-update-info :modelName="$transaction" />
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-between">
                        <a href="{{ route('accounts.show', $transaction->account) }}" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-300 active:bg-gray-300 focus:outline-none focus:border-gray-900 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
                            {{ __('Back to Account') }}
                        </a>
                        
                        <div class="flex space-x-2">
                            @can('update', $transaction)
                                <a href="{{ route('transactions.edit', $transaction) }}" class="inline-flex items-center px-4 py-2 bg-yellow-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-yellow-700 active:bg-yellow-800 focus:outline-none focus:border-yellow-800 focus:ring ring-yellow-300 disabled:opacity-25 transition ease-in-out duration-150">
                                    {{ __('Edit Transaction') }}
                                </a>
                            @endcan
                            
                            @can('delete', $transaction)
                                <form action="{{ route('transactions.destroy', $transaction) }}" method="POST" class="inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 active:bg-red-800 focus:outline-none focus:border-red-800 focus:ring ring-red-300 disabled:opacity-25 transition ease-in-out duration-150" onclick="return confirm('{{ __('Are you sure you want to delete this transaction?') }}')">
                                        {{ __('Delete Transaction') }}
                                    </button>
                                </form>
                            @endcan
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout> 