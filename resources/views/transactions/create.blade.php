@php
use App\Helpers\CurrencyHelper;
$currencies = CurrencyHelper::getAllCurrencies();
$currency = $currencies[$account->currency] ?? null;
$currencySymbol = $currency['symbol'] ?? $account->currency;
@endphp

<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <x-navigation-buttons>
                    <h2 class="font-semibold text-xl text-gray-800 leading-tight ml-4">
                        {{ __('Add Transaction') }}
                    </h2>
                </x-navigation-buttons>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    @include('transactions.partials.form', [
                        'action' => route('accounts.transactions.store', ['account' => $account->id]),
                        'account' => $account,
                        'buttonText' => __('Add Transaction')
                    ])
                </div>
            </div>

            <div class="mt-6 flex space-x-4">
                <x-action-button
                    :href="route('accounts.show', $account)"
                    icon="arrow-left"
                    color="gray"
                    :tooltip="__('Back to Account')"
                    class="px-4 py-2"
                >
                    <span class="ml-1 text-xs uppercase font-semibold">{{ __('Back to Account') }}</span>
                </x-action-button>
            </div>
        </div>
    </div>
</x-app-layout>
