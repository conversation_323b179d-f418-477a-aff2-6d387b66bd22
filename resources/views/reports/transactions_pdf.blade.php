<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Account Transactions Report</title>
    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', <PERSON>l, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            direction: {{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }};
        }
        .container {
            margin: 0 auto;
            width: 100%;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .account-info {
            background-color: #f2f2f2;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .account-info h2 {
            margin-top: 0;
        }
        .account-summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .account-summary div {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            text-align: center;
        }
        .account-summary .income {
            color: green;
        }
        .account-summary .expense {
            color: red;
        }
        .account-summary .balance {
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .income-amount {
            color: green;
        }
        .expense-amount {
            color: red;
        }
        .footer {
            text-align: center;
            font-size: 10px;
            margin-top: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Account Transactions Report</h1>
            <p>Generated on: {{ now()->format('F j, Y, g:i a') }}</p>
        </div>
        
        <div class="account-info">
            <h2>{{ $account->name }}</h2>
            <p>{{ $account->description ?: 'No description' }}</p>
            <div class="account-summary">
                <div class="income">
                    <h3>Total Income</h3>
                    <p>{{ $account->currency }} {{ number_format($account->total_income, 2) }}</p>
                </div>
                <div class="expense">
                    <h3>Total Expenses</h3>
                    <p>{{ $account->currency }} {{ number_format($account->total_expenses, 2) }}</p>
                </div>
                <div class="balance">
                    <h3>Current Balance</h3>
                    <p>{{ $account->currency }} {{ number_format($account->balance, 2) }}</p>
                </div>
            </div>
        </div>
        
        <h2>Transactions</h2>
        
        @if($transactions->isEmpty())
            <p>No transactions found for this account.</p>
        @else
            <table>
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Description</th>
                        <th>Category</th>
                        <th>Type</th>
                        <th>Amount</th>
                        <th>Created By</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($transactions as $transaction)
                        <tr>
                            <td>{{ $transaction->transaction_date->format('Y-m-d') }}</td>
                            <td>{{ $transaction->description }}</td>
                            <td>{{ $transaction->category ?: 'N/A' }}</td>
                            <td>{{ ucfirst($transaction->type) }}</td>
                            <td class="{{ $transaction->type === 'income' ? 'income-amount' : 'expense-amount' }}">
                                {{ $transaction->type === 'income' ? '+' : '-' }}{{ $account->currency }} {{ number_format($transaction->amount, 2) }}
                            </td>
                            <td>{{ $transaction->user->name }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        @endif
        
        <div class="footer">
            <p>This is an automatically generated report from your Personal Accounting App.</p>
        </div>
    </div>
</body>
</html> 