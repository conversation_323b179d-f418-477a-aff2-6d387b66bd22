@props([
    'type' => 'submit',
    'variant' => 'primary',
    'href' => null,
    'form' => null,
    'target' => '_self',
    'method' => 'POST',
    'onclick' => null,
    'size' => 'md'
])

@php
    // Base classes
    $baseClasses = 'inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 ease-in-out';
    
    // Size variants
    $sizeClasses = [
        'xs' => 'px-2.5 py-1.5 text-xs',
        'sm' => 'px-3 py-1.5 text-sm',
        'md' => 'px-4 py-2 text-sm',
        'lg' => 'px-5 py-2.5 text-base',
        'xl' => 'px-6 py-3 text-base'
    ];
    
    // Color variants
    $variantClasses = [
        'primary' => 'bg-gray-800 text-white border-transparent hover:bg-gray-700 active:bg-gray-900 focus:ring-indigo-500 shadow-sm hover:shadow disabled:opacity-60',
        'secondary' => 'bg-white text-gray-700 border border-gray-300 shadow-sm hover:bg-gray-50 hover:text-gray-900 focus:ring-indigo-500 disabled:opacity-60',
        'danger' => 'bg-red-600 text-white border-transparent hover:bg-red-500 active:bg-red-700 focus:ring-red-500 shadow-sm hover:shadow disabled:opacity-60',
        'success' => 'bg-green-600 text-white border-transparent hover:bg-green-500 active:bg-green-700 focus:ring-green-500 shadow-sm hover:shadow disabled:opacity-60',
        'warning' => 'bg-yellow-500 text-white border-transparent hover:bg-yellow-400 active:bg-yellow-600 focus:ring-yellow-500 shadow-sm hover:shadow disabled:opacity-60',
        'info' => 'bg-blue-600 text-white border-transparent hover:bg-blue-500 active:bg-blue-700 focus:ring-blue-500 shadow-sm hover:shadow disabled:opacity-60',
        'link' => 'bg-transparent text-blue-600 hover:text-blue-800 hover:underline focus:ring-blue-500',
    ];
    
    $classes = $baseClasses . ' ' . ($sizeClasses[$size] ?? $sizeClasses['md']) . ' ' . ($variantClasses[$variant] ?? $variantClasses['primary']);
    
    // Additional attributes
    $attributes = $attributes->class([$classes])->merge([
        'type' => $href ? 'button' : $type,
    ]);
@endphp

@if ($href)
    <a href="{{ $href }}" target="{{ $target }}" {{ $attributes }}>
        {{ $slot }}
    </a>
@elseif ($form)
    <form action="{{ $form }}" method="{{ $method }}" class="inline">
        @csrf
        @if ($method !== 'POST')
            @method($method)
        @endif
        <button {{ $attributes }} @if ($onclick) onclick="return {{ $onclick }}" @endif>
            {{ $slot }}
        </button>
    </form>
@else
    <button {{ $attributes }} @if ($onclick) onclick="return {{ $onclick }}" @endif>
        {{ $slot }}
    </button>
@endif 