# UI Component System

This document provides an overview of the UI component system used in the Family Accounting application. These components are designed to promote consistency, reusability, and maintainability across the application's UI.

## Available Components

### Layout Components

- **Card**: Containers for content with consistent styling
- **Page-Header**: Page titles with optional action buttons
- **Page-Content**: Wrapper for main content

### Form Components

For comprehensive form components, see the dedicated form components in `resources/views/components/forms/`. These include:

- **Form Inputs**: Text inputs, textareas, selects, checkboxes, radios, and file uploads
- **Form Layout**: Form sections, groups, and wrappers
- **Form Buttons**: Action buttons and button containers

The form components are built on top of these basic form elements:
- **Input-Label**: Basic label component
- **Text-Input**: Basic input field component
- **Input-Error**: Basic validation error display

For more details, refer to `resources/views/components/forms/README.md`.

### Data Display Components

- **Table**: Table component with responsive design
  - **Table.Head**: Table header component
  - **Table.Body**: Table body component
  - **Table.Th**: Table header cell
  - **Table.Td**: Table data cell
  - **Table.Tr**: Table row
- **Badge**: Status badges for displaying different states
- **Alert**: Alert messages with different types and styles
- **Empty-State**: Component for displaying when no data is available
- **Stat-Card**: Card for displaying key metrics and statistics
- **Currency-Display**: Formatted currency display

### Navigation Components

- **Nav-Link**: Navigation links
- **Dropdown-Link**: Links inside dropdown menus
- **Navigation-Buttons**: Navigation action buttons
- **Responsive-Nav-Link**: Mobile-friendly navigation links

### Utility Components

- **Icon**: SVG icon component with a library of common icons
- **Modal**: Modal dialog component

## Usage Examples

### Card Component

```blade
<x-card title="Card Title" subtitle="Optional subtitle">
    <!-- Card content goes here -->
    
    <x-slot name="footer">
        <!-- Optional footer content -->
    </x-slot>
</x-card>
```

### Table Component

```blade
<x-table>
    <x-table.head>
        <tr>
            <x-table.th>Column 1</x-table.th>
            <x-table.th>Column 2</x-table.th>
            <!-- More columns -->
        </tr>
    </x-table.head>
    <x-table.body>
        @foreach($items as $index => $item)
            <x-table.tr :index="$index" striped>
                <x-table.td>{{ $item->property1 }}</x-table.td>
                <x-table.td textColor="text-green-600">{{ $item->property2 }}</x-table.td>
                <!-- More cells -->
            </x-table.tr>
        @endforeach
    </x-table.body>
</x-table>
```

### Alert Component

```blade
<x-alert type="success" dismissible title="Success!">
    Your action was completed successfully.
</x-alert>
```

### Badge Component

```blade
<x-badge type="success">Active</x-badge>
<x-badge type="warning">Pending</x-badge>
<x-badge type="danger">Error</x-badge>
```

### Empty State Component

```blade
<x-empty-state
    icon="info"
    title="No Results Found"
    message="We couldn't find any items matching your criteria."
    actionUrl="/create"
    actionText="Create New"
    actionType="primary"
/>
```

### Stat Card Component

```blade
<x-stat-card
    title="Total Balance"
    value="1,250.00"
    currency="USD"
    icon="money"
    bgColor="bg-blue-50"
/>
```

### Form Components Examples

#### Basic Form Input

```blade
<x-input-label for="email" :value="__('Email')" />
<x-text-input id="email" type="email" name="email" :value="old('email')" required />
<x-input-error :messages="$errors->get('email')" />
```

#### Advanced Form Input

```blade
<x-forms.inputs.form-input
    name="email"
    label="Email Address"
    type="email"
    placeholder="<EMAIL>"
    required
    :value="old('email')"
    helpText="We'll never share your email with anyone else."
/>
```

#### Form Button

```blade
<x-forms.buttons.form-button variant="primary">Primary Button</x-forms.buttons.form-button>
<x-forms.buttons.form-button variant="secondary">Secondary Button</x-forms.buttons.form-button>
<x-forms.buttons.form-button variant="success" href="/success">Link Button</x-forms.buttons.form-button>
<x-forms.buttons.form-button variant="danger" form="/form/action" method="DELETE">Delete</x-forms.buttons.form-button>
```

### Icon Component

```blade
<x-icon name="plus" size="sm" />
<x-icon name="edit" size="md" color="text-blue-500" />
<x-icon name="trash" size="lg" color="text-red-500" />
```

## Best Practices

1. Use components consistently across the application
2. Pass dynamic properties to customize components rather than hardcoding styles
3. Keep components focused on one responsibility
4. Document any new component functionality
5. Maintain a consistent naming convention
6. Use slot-based content for flexible component composition

## Extending Components

To extend or modify a component:

1. Create a new component or modify the existing one
2. Use props to provide sensible defaults
3. Document the available props
4. Keep the markup semantic and accessible
5. Use Tailwind utility classes consistently 