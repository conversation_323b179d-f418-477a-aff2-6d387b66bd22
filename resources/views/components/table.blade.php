@props([
    'hover' => true,
    'striped' => false,
    'responsive' => true,
    'bordered' => false,
    'shadow' => false,
    'rounded' => true
])

@php
    $baseClasses = 'min-w-full divide-y divide-gray-200';
    
    $containerClasses = [];
    
    if ($responsive) {
        $containerClasses[] = 'overflow-x-auto';
    }
    
    if ($bordered) {
        $baseClasses .= ' border border-gray-200';
    }
    
    if ($shadow) {
        $containerClasses[] = 'shadow-sm';
    }
    
    if ($rounded) {
        $containerClasses[] = 'sm:rounded-lg';
        if ($bordered) {
            $baseClasses .= ' sm:rounded-lg';
        }
    }
    
    // Additional attributes
    $attributes = $attributes->class([$baseClasses]);
@endphp

@if($responsive)
<div class="{{ implode(' ', $containerClasses) }}">
@endif
    <table {{ $attributes }}>
        {{ $slot }}
    </table>
@if($responsive)
</div>
@endif 