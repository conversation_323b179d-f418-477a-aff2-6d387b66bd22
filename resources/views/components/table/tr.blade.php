@props([
    'hover' => true,
    'highlight' => false,
    'striped' => false,
    'index' => null,
    'clickable' => false
])

@php
    $baseClasses = 'transition-colors duration-150';
    
    if ($hover) {
        $baseClasses .= ' hover:bg-gray-50';
    }
    
    if ($highlight) {
        $baseClasses .= ' bg-yellow-50';
    }
    
    if ($striped && $index !== null) {
        $baseClasses .= $index % 2 === 0 ? ' bg-white' : ' bg-gray-50/50';
    }
    
    if ($clickable) {
        $baseClasses .= ' cursor-pointer';
    }
    
    // Additional attributes
    $attributes = $attributes->class([$baseClasses]);
@endphp

<tr {{ $attributes }}>
    {{ $slot }}
</tr> 