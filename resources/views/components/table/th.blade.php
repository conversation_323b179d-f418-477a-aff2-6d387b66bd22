@props([
    'align' => 'left',
    'width' => null,
    'sortable' => false,
    'sortDirection' => null,
    'sortColumn' => null,
    'currentColumn' => null
])

@php
    $baseClasses = 'px-6 py-3.5 text-xs font-medium uppercase tracking-wider bg-gray-50 border-b';
    
    // Text alignment
    if ($align === 'left') {
        $baseClasses .= ' text-left';
    } elseif ($align === 'center') {
        $baseClasses .= ' text-center';
    } elseif ($align === 'right') {
        $baseClasses .= ' text-right';
    }
    
    // Width
    if ($width) {
        $baseClasses .= ' w-' . $width;
    }
    
    // Additional attributes
    $attributes = $attributes->class([$baseClasses]);
@endphp

<th {{ $attributes }} scope="col">
    <div class="flex items-center space-x-1 text-gray-700">
        <span>{{ $slot }}</span>
        
        @if($sortable)
            @if($currentColumn === $sortColumn)
                @if($sortDirection === 'asc')
                    <svg class="w-4 h-4 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                    </svg>
                @else
                    <svg class="w-4 h-4 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                @endif
            @else
                <svg class="w-4 h-4 text-gray-400 opacity-0 group-hover:opacity-100" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                </svg>
            @endif
        @endif
    </div>
</th> 