@props([
    'align' => 'left',
    'nowrap' => true,
    'width' => null,
    'textColor' => 'text-gray-500',
    'fontWeight' => null
])

@php
    $baseClasses = 'px-6 py-4 text-sm';
    
    // Text wrapping
    if ($nowrap) {
        $baseClasses .= ' whitespace-nowrap';
    }
    
    // Text alignment
    if ($align === 'left') {
        $baseClasses .= ' text-left';
    } elseif ($align === 'center') {
        $baseClasses .= ' text-center';
    } elseif ($align === 'right') {
        $baseClasses .= ' text-right';
    }
    
    // Text color
    $baseClasses .= ' ' . $textColor;
    
    // Font weight
    if ($fontWeight) {
        $baseClasses .= ' ' . $fontWeight;
    }
    
    // Width
    if ($width) {
        $baseClasses .= ' w-' . $width;
    }
    
    // Additional attributes
    $attributes = $attributes->class([$baseClasses]);
@endphp

<td {{ $attributes }}>
    {{ $slot }}
</td> 