@props(['currencyCode', 'amount' => null, 'showFlag' => true, 'showSymbol' => true])

@php
use App\Helpers\CurrencyHelper;
$currencies = CurrencyHelper::getAllCurrencies();
$currency = $currencies[$currencyCode] ?? null;
@endphp

@if ($currency)
    <span {{ $attributes->merge(['class' => 'inline-flex items-center']) }}>
        @if($showFlag && isset($currency['flag']))
            <span class="mr-1">{{ $currency['flag'] }}</span>
        @endif
        
        @if($showSymbol && isset($currency['symbol']))
            <span class="font-semibold">{{ $currency['symbol'] }}</span>
        @else
            <span class="font-semibold">{{ $currencyCode }}</span>
        @endif
        
        @if($amount !== null)
            <span>{{ number_format((float)$amount, 2) }}</span>
        @endif
    </span>
@else
    <span {{ $attributes }}>
        {{ $currencyCode }} 
        @if($amount !== null)
            {{ number_format((float)$amount, 2) }}
        @endif
    </span>
@endif 