@props([
    'title' => null,
    'subtitle' => null,
    'titleSize' => 'text-lg',
    'footer' => null,
    'headerBg' => 'bg-white',
    'bodyBg' => 'bg-white',
    'footerBg' => 'bg-gray-50',
    'padding' => 'p-6',
    'rounded' => true,
    'shadow' => true,
    'border' => true,
    'marginBottom' => 'mb-6',
    'hover' => false
])

<div class="{{ $shadow ? 'shadow-md' : '' }} {{ $rounded ? 'rounded-lg' : '' }} overflow-hidden {{ $marginBottom }} {{ $hover ? 'transition-all duration-200 hover:shadow-lg' : '' }}">
    <div class="{{ $headerBg }} {{ $padding }} {{ $border ? 'border-b border-gray-200' : '' }}">
        @if($title)
            <div class="flex justify-between items-center mb-4">
                <h2 class="{{ $titleSize }} font-semibold text-gray-900">{{ $title }}</h2>
                
                @if(isset($headerActions))
                    <div class="flex items-center space-x-2">
                        {{ $headerActions }}
                    </div>
                @endif
            </div>
            
            @if($subtitle)
                <p class="text-sm text-gray-600 mb-4">{{ $subtitle }}</p>
            @endif
        @endif
        
        {{ $slot }}
    </div>
    
    @if($footer)
        <div class="{{ $footerBg }} {{ $padding }} {{ $border ? 'border-t border-gray-200' : '' }}">
            {{ $footer }}
        </div>
    @endif
</div> 