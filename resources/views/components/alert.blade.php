@props([
    'type' => 'default',
    'dismissible' => false,
    'title' => null,
    'rounded' => true,
    'border' => true,
    'icon' => null,
    'shadow' => false
])

@php
    // Base classes
    $baseClasses = 'p-4 my-4';
    
    // Rounded
    if ($rounded) {
        $baseClasses .= ' rounded-lg';
    }
    
    // Shadow
    if ($shadow) {
        $baseClasses .= ' shadow-sm';
    }
    
    // Type (color)
    $typeClasses = [
        'default' => 'bg-gray-50 text-gray-700',
        'primary' => 'bg-blue-50 text-blue-800',
        'success' => 'bg-green-50 text-green-800',
        'info' => 'bg-blue-50 text-blue-800',
        'warning' => 'bg-yellow-50 text-yellow-800',
        'danger' => 'bg-red-50 text-red-800',
    ];
    
    $baseClasses .= ' ' . ($typeClasses[$type] ?? $typeClasses['default']);
    
    // Border
    if ($border) {
        $borderClasses = [
            'default' => 'border-l-4 border-gray-400',
            'primary' => 'border-l-4 border-blue-500',
            'success' => 'border-l-4 border-green-500',
            'info' => 'border-l-4 border-blue-500',
            'warning' => 'border-l-4 border-yellow-500',
            'danger' => 'border-l-4 border-red-500',
        ];
        
        $baseClasses .= ' ' . ($borderClasses[$type] ?? $borderClasses['default']);
    }
    
    // Merge additional attributes
    $attributes = $attributes->class([$baseClasses]);
    
    // Icon based on type
    if (!$icon) {
        $icons = [
            'default' => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>',
            'success' => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>',
            'warning' => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
            'info' => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>',
            'danger' => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>',
        ];
        
        $icon = $icons[$type] ?? $icons['default'];
    }
@endphp

<div {{ $attributes }} role="alert" aria-live="assertive" aria-atomic="true">
    <div class="flex">
        @if($icon)
            <div class="flex-shrink-0 mr-3">
                {!! $icon !!}
            </div>
        @endif
        
        <div class="flex-grow">
            @if($title)
                <h3 class="text-sm font-semibold mb-1">{{ $title }}</h3>
            @endif
            <div class="text-sm">
                {{ $slot }}
            </div>
        </div>
        
        @if($dismissible)
            <div class="ml-auto">
                <button type="button" class="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 rounded" aria-label="Dismiss" @click="$el.parentNode.parentNode.remove()">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        @endif
    </div>
</div> 