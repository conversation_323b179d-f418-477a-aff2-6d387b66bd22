<div id="error-recovery-alert" class="fixed inset-0 flex items-center justify-center z-50" role="alert">
    <div class="fixed inset-0 bg-gray-900 bg-opacity-50"></div>
    <div class="bg-white rounded-lg shadow-xl overflow-hidden max-w-md w-full z-10">
        <!-- Error header -->
        <div class="bg-red-600 px-4 py-3 text-white flex justify-between items-center">
            <div class="flex items-center">
                <svg class="h-6 w-6 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <h3 class="text-lg font-medium">{{ __('errors.error_occurred') }}</h3>
            </div>
            <button onclick="closeErrorAlert()" class="text-white hover:text-gray-200">
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <!-- Error content -->
        <div class="p-5">
            <p class="text-gray-700 mb-4">{{ session('error') }}</p>
            
            <!-- Error context from URL -->
            @if(session('error_context'))
            <div class="bg-gray-100 p-3 rounded mb-4 text-sm">
                <p class="font-semibold mb-1">{{ __('errors.details') }}:</p>
                <p>{{ session('error_context') }}</p>
            </div>
            @endif
            
            <!-- Recovery options -->
            <div class="flex flex-col space-y-3 mt-5">
                <!-- Retry operation button if URL is available -->
                @if(url()->previous() && url()->previous() != url()->current())
                <a href="{{ url()->previous() }}" class="inline-flex justify-center items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    {{ __('errors.try_again') }}
                </a>
                @endif
                
                <!-- Go back button -->
                <a href="{{ url()->previous() }}" class="inline-flex justify-center items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    {{ __('errors.go_back') }}
                </a>
                
                <!-- Home button -->
                <a href="{{ route('dashboard') }}" class="inline-flex justify-center items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    {{ __('errors.go_to_dashboard') }}
                </a>
            </div>
        </div>
    </div>
</div>

<script>
    function closeErrorAlert() {
        document.getElementById('error-recovery-alert').remove();
    }
    
    // Auto-focus the Try Again button if it exists
    document.addEventListener('DOMContentLoaded', function() {
        const retryButton = document.querySelector('#error-recovery-alert a:first-child');
        if (retryButton) {
            retryButton.focus();
        }
    });
</script> 