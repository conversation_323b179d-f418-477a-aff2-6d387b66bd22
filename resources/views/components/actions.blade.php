@props([
    'align' => 'left',
    'class' => '',
])

@php
    $alignmentClasses = [
        'left' => 'justify-start',
        'center' => 'justify-center',
        'right' => 'justify-end',
        'between' => 'justify-between',
        'around' => 'justify-around',
        'evenly' => 'justify-evenly',
    ];
    
    $baseClasses = 'flex items-center gap-1 flex-wrap';
    
    // Add alignment classes
    $baseClasses .= ' ' . ($alignmentClasses[$align] ?? $alignmentClasses['left']);
    
    // Add custom classes
    if ($class) {
        $baseClasses .= ' ' . $class;
    }
@endphp

<div class="{{ $baseClasses }}">
    {{ $slot }}
</div> 