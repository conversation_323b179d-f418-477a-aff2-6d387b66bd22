@props([
    'type' => 'default',
    'size' => 'sm',
    'rounded' => 'full',
    'bordered' => false,
    'dot' => false,
    'icon' => null
])

@php
    // Base classes
    $baseClasses = 'inline-flex items-center justify-center whitespace-nowrap font-medium';
    
    // Size
    $sizeClasses = [
        'xs' => 'px-1.5 py-0.5 text-xs',
        'sm' => 'px-2 py-0.5 text-xs',
        'md' => 'px-2.5 py-1 text-sm',
        'lg' => 'px-3 py-1.5 text-base'
    ];
    
    $baseClasses .= ' ' . ($sizeClasses[$size] ?? $sizeClasses['sm']);
    
    // Rounded
    if ($rounded === 'full') {
        $baseClasses .= ' rounded-full';
    } elseif ($rounded === 'md') {
        $baseClasses .= ' rounded-md';
    } elseif ($rounded === 'none') {
        // No rounding
    } else {
        $baseClasses .= ' rounded-' . $rounded;
    }
    
    // Type (color)
    $typeClasses = [
        'default' => 'bg-gray-100 text-gray-800',
        'primary' => 'bg-blue-100 text-blue-800',
        'success' => 'bg-green-100 text-green-800',
        'info' => 'bg-indigo-100 text-indigo-800',
        'warning' => 'bg-yellow-100 text-yellow-800',
        'danger' => 'bg-red-100 text-red-800',
        'owner' => 'bg-emerald-100 text-emerald-800',
        'shared' => 'bg-sky-100 text-sky-800',
        'purple' => 'bg-purple-100 text-purple-800',
        'pink' => 'bg-pink-100 text-pink-800',
        'outline' => 'bg-white text-gray-700 border border-gray-300',
    ];
    
    $baseClasses .= ' ' . ($typeClasses[$type] ?? $typeClasses['default']);
    
    // Bordered
    if ($bordered && $type !== 'outline') {
        $borderClasses = [
            'default' => 'border border-gray-300',
            'primary' => 'border border-blue-300',
            'success' => 'border border-green-300',
            'info' => 'border border-indigo-300',
            'warning' => 'border border-yellow-300',
            'danger' => 'border border-red-300',
            'owner' => 'border border-emerald-300',
            'shared' => 'border border-sky-300',
            'purple' => 'border border-purple-300',
            'pink' => 'border border-pink-300',
        ];
        
        $baseClasses .= ' ' . ($borderClasses[$type] ?? $borderClasses['default']);
    }
    
    // Merge additional attributes
    $attributes = $attributes->class([$baseClasses]);
@endphp

<span {{ $attributes }}>
    @if($dot)
    <span class="flex h-2 w-2 relative -ml-0.5 mr-1.5">
        <span class="{{ str_replace('bg-', 'bg-', $typeClasses[$type] ?? $typeClasses['default']) }} absolute inline-flex h-full w-full rounded-full opacity-75 animate-ping"></span>
        <span class="{{ str_replace('bg-', 'bg-', $typeClasses[$type] ?? $typeClasses['default']) }} relative inline-flex rounded-full h-2 w-2"></span>
    </span>
    @endif
    
    @if($icon)
    <span class="mr-1">
        {!! $icon !!}
    </span>
    @endif
    
    {{ $slot }}
</span> 