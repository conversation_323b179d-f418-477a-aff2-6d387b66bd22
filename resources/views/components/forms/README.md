# Form Components

This directory contains a modular, reusable form system for Laravel Blade views. These components are designed to promote consistency, reusability, and maintainability across the application's forms.

## Directory Structure

```
forms/
├── buttons/
│   ├── form-actions.blade.php
│   └── form-button.blade.php
├── elements/
│   ├── form-error.blade.php
│   ├── form-label.blade.php
│   └── form-text-input.blade.php
├── inputs/
│   ├── form-checkbox.blade.php
│   ├── form-checkbox-group.blade.php
│   ├── form-file.blade.php
│   ├── form-input.blade.php
│   ├── form-radio.blade.php
│   ├── form-radio-group.blade.php
│   ├── form-select.blade.php
│   └── form-textarea.blade.php
└── layout/
    ├── form-group.blade.php
    ├── form-layout.blade.php
    └── form-section.blade.php
```

## Available Components

### Basic Elements (in `elements/`)

- **form-label.blade.php**: Basic label element
- **form-error.blade.php**: Error message display for validation errors
- **form-text-input.blade.php**: Basic input element

### Layout Components (in `layout/`)

- **form-layout.blade.php**: Base form wrapper with CSRF protection and method spoofing
- **form-section.blade.php**: Groups related form fields with optional title and description
- **form-group.blade.php**: Wraps form controls with label, error message, and help text

### Input Components (in `inputs/`)

- **form-input.blade.php**: Text, email, number, password inputs
- **form-textarea.blade.php**: Multiline text input
- **form-select.blade.php**: Dropdown selection
- **form-checkbox.blade.php**: Single checkbox
- **form-radio.blade.php**: Single radio button
- **form-checkbox-group.blade.php**: Group of checkboxes
- **form-radio-group.blade.php**: Group of radio buttons
- **form-file.blade.php**: File upload

### Button Components (in `buttons/`)

- **form-button.blade.php**: Form submission and other actions
- **form-actions.blade.php**: Container for form buttons with consistent styling

## Usage Examples

### Basic Form Layout

```blade
<x-forms.layout.form-layout :action="route('posts.store')">
    <!-- Form fields go here -->
    
    <x-forms.buttons.form-actions>
        <x-forms.buttons.form-button type="submit" variant="primary">
            {{ __('Save') }}
        </x-forms.buttons.form-button>
        
        <x-forms.buttons.form-button :href="route('posts.index')" variant="secondary">
            {{ __('Cancel') }}
        </x-forms.buttons.form-button>
    </x-forms.buttons.form-actions>
</x-forms.layout.form-layout>
```

### Form with Sections

```blade
<x-forms.layout.form-layout :action="route('posts.store')">
    <x-forms.layout.form-section title="Basic Information" description="Enter the basic details for this post.">
        <x-forms.inputs.form-input 
            name="title" 
            label="Post Title" 
            required 
            :value="old('title')" 
        />
        
        <x-forms.inputs.form-textarea 
            name="description" 
            label="Description" 
            :value="old('description')"
            helpText="A brief summary of the post content."
        />
    </x-forms.layout.form-section>
    
    <x-forms.layout.form-section title="Publishing Options">
        <x-forms.inputs.form-checkbox
            name="published"
            label="Publish immediately"
            :checked="old('published', true)"
        />
        
        <x-forms.inputs.form-select
            name="category_id"
            label="Category"
            :options="$categories"
            :value="old('category_id')"
            required
        />
    </x-forms.layout.form-section>
    
    <x-forms.buttons.form-actions>
        <x-forms.buttons.form-button type="submit" variant="primary">
            {{ __('Save Post') }}
        </x-forms.buttons.form-button>
    </x-forms.buttons.form-actions>
</x-forms.layout.form-layout>
```

### Individual Form Controls

#### Text Input

```blade
<x-forms.inputs.form-input
    name="email"
    label="Email Address"
    type="email"
    placeholder="<EMAIL>"
    required
    :value="old('email')"
    helpText="We'll never share your email with anyone else."
/>
```

#### Select Dropdown

```blade
<x-forms.inputs.form-select
    name="country"
    label="Country"
    :options="$countries"
    :value="old('country')"
    placeholder="Select your country"
    required
/>
```

#### Textarea

```blade
<x-forms.inputs.form-textarea
    name="bio"
    label="Biography"
    :value="old('bio')"
    rows="5"
    helpText="Tell us about yourself."
/>
```

#### Checkbox

```blade
<x-forms.inputs.form-checkbox
    name="agree_terms"
    label="I agree to the terms and conditions"
    :checked="old('agree_terms')"
    required
/>
```

#### Radio Buttons

```blade
<x-forms.inputs.form-radio-group
    name="gender"
    label="Gender"
    :options="['male' => 'Male', 'female' => 'Female', 'other' => 'Other']"
    :value="old('gender')"
/>
```

#### File Upload

```blade
<x-forms.inputs.form-file
    name="profile_image"
    label="Profile Image"
    accept="image/*"
    helpText="JPEG, PNG, or GIF files only. Max size: 2MB."
/>
```

#### Button

```blade
<x-forms.buttons.form-button variant="primary">
    {{ __('Submit') }}
</x-forms.buttons.form-button>

<x-forms.buttons.form-button variant="secondary" href="/cancel">
    {{ __('Cancel') }}
</x-forms.buttons.form-button>

<x-forms.buttons.form-button variant="danger" form="/some-action" method="DELETE">
    {{ __('Delete') }}
</x-forms.buttons.form-button>
```

## Common Props

All input components support these common props:

- **name**: (required) The form field name
- **label**: The label text for the form field
- **value**: The current value of the field
- **placeholder**: Placeholder text
- **required**: Mark the field as required
- **disabled**: Disable the field
- **readonly**: Make the field read-only
- **helpText**: Additional helper text below the field
- **error**: Custom error message (defaults to Laravel validation errors)

## Integration with Laravel Validation

The form components automatically integrate with Laravel's validation system:

- They automatically display validation errors from the Laravel error bag
- They preserve old input values after validation failures
- They highlight fields with errors in red

For example, if a validation error occurs for a field:

```blade
<x-forms.inputs.form-input name="email" label="Email" />
```

The component will automatically:
1. Display any validation errors for "email" below the field
2. Highlight the field in red to indicate an error
3. Preserve the old input value the user entered
