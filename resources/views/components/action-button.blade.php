@props([
    'href' => null,
    'method' => 'GET',
    'action' => null,
    'type' => 'button',
    'color' => 'blue',
    'icon' => null,
    'tooltip' => null,
    'onclick' => null,
    'class' => '',
])

@php
    $colorClasses = [
        'blue' => 'text-blue-600 hover:text-blue-800 hover:bg-blue-100',
        'red' => 'text-red-600 hover:text-red-800 hover:bg-red-100',
        'green' => 'text-green-600 hover:text-green-800 hover:bg-green-100',
        'yellow' => 'text-yellow-600 hover:text-yellow-800 hover:bg-yellow-100',
        'gray' => 'text-gray-600 hover:text-gray-800 hover:bg-gray-100',
    ];
    
    $baseClasses = 'p-2 inline-flex items-center justify-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-opacity-50';
    
    // Add color classes
    $baseClasses .= ' ' . ($colorClasses[$color] ?? $colorClasses['blue']);
    
    // Focus ring color
    $baseClasses .= ' focus:ring-' . $color . '-300';
    
    // Add custom classes
    if ($class) {
        $baseClasses .= ' ' . $class;
    }
@endphp

@if($href && $method === 'GET')
    <a 
        href="{{ $href }}" 
        class="{{ $baseClasses }}"
        @if($tooltip) title="{{ $tooltip }}" @endif
        @if($onclick) onclick="{{ $onclick }}" @endif
    >
        @if($icon)
            <x-icon :name="$icon" class="w-5 h-5" />
        @endif
        {{ $slot }}
    </a>
@elseif($action && $method !== 'GET')
    <form action="{{ $action }}" method="POST" class="inline">
        @csrf
        @if($method !== 'POST')
            @method($method)
        @endif
        <button 
            type="{{ $type }}"
            class="{{ $baseClasses }}"
            @if($tooltip) title="{{ $tooltip }}" @endif
            @if($onclick) onclick="{{ $onclick }}" @endif
        >
            @if($icon)
                <x-icon :name="$icon" class="w-5 h-5" />
            @endif
            {{ $slot }}
        </button>
    </form>
@else
    <button 
        type="{{ $type }}"
        class="{{ $baseClasses }}"
        @if($tooltip) title="{{ $tooltip }}" @endif
        @if($onclick) onclick="{{ $onclick }}" @endif
    >
        @if($icon)
            <x-icon :name="$icon" class="w-5 h-5" />
        @endif
        {{ $slot }}
    </button>
@endif 