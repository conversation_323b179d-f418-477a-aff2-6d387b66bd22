@props([
    'title' => null,
    'value' => null,
    'icon' => null,
    'iconColor' => 'text-blue-800',
    'bgColor' => 'bg-white',
    'textColor' => 'text-blue-900',
    'titleColor' => 'text-gray-600',
    'change' => null,
    'changeType' => null,
    'currency' => null,
    'shadow' => true,
    'border' => true,
    'hover' => true,
    'link' => null,
    'accent' => 'blue'
])

@php
    $colorMap = [
        'blue' => [
            'bg' => 'bg-white',
            'text' => 'text-blue-900',
            'title' => 'text-gray-600',
            'icon' => 'text-blue-500',
            'accent' => 'border-blue-500',
        ],
        'green' => [
            'bg' => 'bg-white',
            'text' => 'text-green-900',
            'title' => 'text-gray-600',
            'icon' => 'text-green-500',
            'accent' => 'border-green-500',
        ],
        'red' => [
            'bg' => 'bg-white',
            'text' => 'text-red-900',
            'title' => 'text-gray-600',
            'icon' => 'text-red-500',
            'accent' => 'border-red-500',
        ],
        'yellow' => [
            'bg' => 'bg-white',
            'text' => 'text-yellow-900',
            'title' => 'text-gray-600',
            'icon' => 'text-yellow-500',
            'accent' => 'border-yellow-500',
        ],
        'purple' => [
            'bg' => 'bg-white',
            'text' => 'text-purple-900',
            'title' => 'text-gray-600',
            'icon' => 'text-purple-500',
            'accent' => 'border-purple-500',
        ],
        'gray' => [
            'bg' => 'bg-white',
            'text' => 'text-gray-900',
            'title' => 'text-gray-600',
            'icon' => 'text-gray-500',
            'accent' => 'border-gray-500',
        ],
    ];
    
    $containerClasses = "$bgColor relative p-5 rounded-lg ";
    
    if ($shadow) {
        $containerClasses .= "shadow-sm ";
    }
    
    if ($border) {
        $containerClasses .= "border border-gray-200 ";
    }
    
    if ($accent && isset($colorMap[$accent])) {
        $containerClasses .= "border-t-4 {$colorMap[$accent]['accent']} ";
    }
    
    if ($hover) {
        $containerClasses .= "transition duration-200 ease-in-out " . ($link ? "hover:shadow-md cursor-pointer" : "");
    }
@endphp

@if($link)
<a href="{{ $link }}" class="{{ $containerClasses }}">
@else
<div class="{{ $containerClasses }}">
@endif
    <div class="flex justify-between items-start">
        <div>
            <h3 class="text-sm font-medium {{ $titleColor }} mb-1">{{ $title }}</h3>
            <p class="text-2xl font-bold {{ $textColor }}">
                @if($currency)
                    <x-currency-display :currencyCode="$currency" :amount="$value" />
                @else
                    {{ $value }}
                @endif
            </p>
            
            @if($change !== null)
                <div class="flex items-center mt-2">
                    <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium {{ $changeType === 'positive' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                        @if($changeType === 'positive')
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5 5 5M7 17l5-5 5 5"></path>
                            </svg>
                        @else
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 7l-5 5-5-5m10 10l-5-5-5 5"></path>
                            </svg>
                        @endif
                        {{ $changeType === 'positive' ? '+' : '' }}{{ $change }}%
                    </span>
                </div>
            @endif
        </div>
        
        @if($icon)
            <div class="rounded-full p-3 {{ str_replace('text-', 'bg-', $iconColor) . '/' . '10' }}">
                <x-icon :name="$icon" size="lg" :color="$iconColor" />
            </div>
        @endif
    </div>
@if($link)
</a>
@else
</div>
@endif 