<?php

namespace App\Models;

use App\Traits\UserAccountTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, HasRoles, Notifiable, SoftDeletes, UserAccountTrait;

    protected $fillable = [
        'name',
        'email',
        'mobile_number',
        'password',
        'allow_search_by_mobile',
        'family_invitation_mode',
        'settings',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'mobile_verified_at' => 'datetime',
            'password' => 'hashed',
            'allow_search_by_mobile' => 'boolean',
            'settings' => 'array',
        ];
    }

    /**
     * Set default values for new instances
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($user) {
            // Set default settings if not already set
            if (empty($user->settings)) {
                $user->settings = [
                    'allow_anyone_to_add_to_families' => false,
                    'notification_preferences' => [
                        'email' => true,
                        'push' => true,
                    ],
                ];
            }
        });
    }

    public function ownedAccounts(): HasMany
    {
        return $this->hasMany(Account::class, 'owner_id');
    }

    public function accounts(): BelongsToMany
    {
        return $this->belongsToMany(Account::class)
            ->withPivot('permission')
            ->withTimestamps();
    }

    public function ownedFamilies(): HasMany
    {
        return $this->hasMany(Family::class, 'owner_id');
    }

    public function families(): BelongsToMany
    {
        return $this->belongsToMany(Family::class)
            ->withTimestamps();
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    public function familyInvitations(): HasMany
    {
        return $this->hasMany(FamilyInvitation::class);
    }

    public function sentFamilyInvitations(): HasMany
    {
        return $this->hasMany(FamilyInvitation::class, 'invited_by');
    }

    public function scopeAllowSearchByMobile($query)
    {
        return $query->where('allow_search_by_mobile', true);
    }

    public static function findByMobileNumber($mobileNumber)
    {
        return self::allowSearchByMobile()
            ->where('mobile_number', $mobileNumber)
            ->first();
    }

    public function allowsAnyoneToAddToFamilies(): bool
    {
        return $this->family_invitation_mode === 'anyone' || 
               ($this->settings['allow_anyone_to_add_to_families'] ?? false);
    }
}
