<?php

namespace App\Models;

use App\Notifications\FamilyInvitationNotification;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Family extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'owner_id'
    ];

    /**
     * Get the owner of the family.
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * Get the users that belong to the family.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class)
            ->withTimestamps();
    }

    /**
     * Get the accounts shared with this family.
     */
    public function accounts(): BelongsToMany
    {
        return $this->belongsToMany(Account::class, 'family_account')
            ->withPivot('permission')
            ->withTimestamps();
    }

    /**
     * Get the invitations for this family.
     */
    public function invitations(): HasMany
    {
        return $this->hasMany(FamilyInvitation::class);
    }

    /**
     * Add a user to the family.
     *
     * @param User $user The user to add
     * @param User $inviter The user who is adding the member
     * @param string|null $relationship The relationship of the user to the family
     * @return bool|FamilyInvitation
     */
    public function addMember(User $user, User $inviter, ?string $relationship = null)
    {
        // Check if user is already a member
        if ($this->users->contains($user->id)) {
            return true;
        }

        // If user is the owner, add them as a member
        if ($user->id === $this->owner_id) {
            $this->users()->attach($user->id, ['relationship' => $relationship]);
            return true;
        }

        // Get user privacy settings
        $allowsAnyone = $user->settings['allow_anyone_to_add_to_families'] ?? false;

        // If user allows anyone to add them, or the inviter is the user being added, add them directly
        if ($allowsAnyone || $user->id === $inviter->id) {
            $this->users()->attach($user->id, ['relationship' => $relationship]);
            return true;
        }

        // Check if there's already a pending invitation
        $existingInvitation = $this->invitations()
            ->where('user_id', $user->id)
            ->where('status', 'pending')
            ->first();

        if ($existingInvitation) {
            return $existingInvitation;
        }

        // Otherwise, create and send an invitation
        return $this->createInvitation($user, $inviter, $relationship);
    }

    /**
     * Create and send an invitation to a user.
     *
     * @param User $user
     * @param User $inviter
     * @param string|null $relationship
     * @return FamilyInvitation
     */
    protected function createInvitation(User $user, User $inviter, ?string $relationship = null): FamilyInvitation
    {
        // Create a unique token
        $token = Str::random(64);

        // Create the invitation
        $invitation = $this->invitations()->create([
            'user_id' => $user->id,
            'invited_by' => $inviter->id,
            'token' => $token,
            'relationship' => $relationship,
            'status' => 'pending',
            'expires_at' => now()->addDays(7),
        ]);

        // Send the notification
        $user->notify(new FamilyInvitationNotification($this, $inviter, $token));

        return $invitation;
    }

    public function isOwner(): bool
    {
        if(auth()->check()) {
            return auth()->id() === $this->owner_id;
        }

        return false;
    }

}
