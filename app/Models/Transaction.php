<?php

namespace App\Models;

use App\Traits\TransactionQueryTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Transaction extends Model
{
    use HasFactory, SoftDeletes, TransactionQueryTrait;

    protected $fillable = [
        'account_id',
        'user_id',
        'description',
        'type',
        'amount',
        'category',
        'tags',
        'transaction_date',
        'notes',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'tags' => 'array',
        'transaction_date' => 'date',
    ];

    /**
     * For backward compatibility - maps 'date' to 'transaction_date'
     */
    public function setDateAttribute($value)
    {
        $this->attributes['transaction_date'] = $value;
    }

    /**
     * For backward compatibility - gets 'transaction_date' when 'date' is accessed
     */
    public function getDateAttribute()
    {
        return $this->transaction_date;
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    protected static function booted(): void
    {
        static::saved(function (Transaction $transaction) {
            $transaction->account->recalculateBalance();
        });

        static::deleted(function (Transaction $transaction) {
            $transaction->account->recalculateBalance();
        });
    }
}
