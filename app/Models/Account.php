<?php

namespace App\Models;

use App\Traits\AccountScopeTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Account extends Model
{
    use HasFactory, SoftDeletes, AccountScopeTrait;

    protected $fillable = [
        'name',
        'description',
        'owner_id',
        'currency',
    ];

    protected $casts = [
        'balance' => 'decimal:2',
        'total_income' => 'decimal:2',
        'total_expenses' => 'decimal:2',
    ];

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class)
            ->withPivot('permission')
            ->withTimestamps();
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    public function families(): BelongsToMany
    {
        return $this->belongsToMany(Family::class, 'family_account')
            ->withPivot('permission')
            ->withTimestamps();
    }

    public function recalculateBalance(): void
    {
        $totalIncome = $this->transactions()->where('type', 'income')->sum('amount');
        $totalExpenses = $this->transactions()->where('type', 'expense')->sum('amount');
        
        $this->total_income = $totalIncome;
        $this->total_expenses = $totalExpenses;
        $this->balance = $totalIncome - $totalExpenses;
        $this->save();
    }
}
