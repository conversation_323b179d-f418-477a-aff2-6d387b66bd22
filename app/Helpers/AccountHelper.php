<?php

namespace App\Helpers;

use App\Models\Account;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class AccountHelper
{
    public static function getUserOwnedAccounts(?User $user = null): array
    {
        $user = $user ?? Auth::user();
        return $user->getOwnedAccountsWithDetails(['transactions'])->toArray();
    }
    
    public static function getUserSharedAccounts(?User $user = null): array
    {
        $user = $user ?? Auth::user();
        return $user->getSharedAccountsWithDetails(['transactions'])->toArray();
    }
    
    public static function getRecentTransactions(?User $user = null, int $limit = 10): array
    {
        $user = $user ?? Auth::user();
        return $user->getRecentTransactions($limit)->toArray();
    }
    
    public static function formatCurrency(float $amount, string $currency = 'USD'): string
    {
        return number_format($amount, 2) . ' ' . $currency;
    }
    
    public static function getAccountBalance(Account $account): float
    {
        return $account->balance;
    }
    
    public static function getTotalBalance(?User $user = null): float
    {
        $user = $user ?? Auth::user();
        $ownedAccounts = $user->ownedAccounts;
        $sharedAccounts = $user->accounts;
        
        $totalBalance = 0;
        
        foreach ($ownedAccounts as $account) {
            $totalBalance += $account->balance;
        }
        
        foreach ($sharedAccounts as $account) {
            $totalBalance += $account->balance;
        }
        
        return $totalBalance;
    }
    
    public static function getTransactionTypeClass(string $type): string
    {
        return $type === 'income' ? 'text-success' : 'text-danger';
    }
    
    public static function getTransactionIcon(string $type): string
    {
        return $type === 'income' ? 'arrow-down' : 'arrow-up';
    }
} 