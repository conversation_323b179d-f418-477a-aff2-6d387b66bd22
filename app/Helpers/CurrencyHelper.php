<?php

namespace App\Helpers;

class CurrencyHelper
{
    /**
     * Get all available currencies with their flags.
     * Includes all Arabic countries' currencies.
     *
     * @return array
     */
    public static function getAllCurrencies(): array
    {
        return [
            // Arabic countries currencies
            'DZD' => ['name' => 'Algerian Dinar', 'symbol' => 'د.ج', 'flag' => '🇩🇿', 'country' => 'Algeria'],
            'BHD' => ['name' => 'Bahraini Dinar', 'symbol' => '.د.ب', 'flag' => '🇧🇭', 'country' => 'Bahrain'],
            'KMF' => ['name' => 'Comorian Franc', 'symbol' => 'CF', 'flag' => '🇰🇲', 'country' => 'Comoros'],
            'DJF' => ['name' => 'Djiboutian Franc', 'symbol' => 'Fdj', 'flag' => '🇩🇯', 'country' => 'Djibouti'],
            'EGP' => ['name' => 'Egyptian Pound', 'symbol' => 'ج.م', 'flag' => '🇪🇬', 'country' => 'Egypt'],
            'IQD' => ['name' => 'Iraqi Dinar', 'symbol' => 'ع.د', 'flag' => '🇮🇶', 'country' => 'Iraq'],
            'JOD' => ['name' => 'Jordanian Dinar', 'symbol' => 'د.ا', 'flag' => '🇯🇴', 'country' => 'Jordan'],
            'KWD' => ['name' => 'Kuwaiti Dinar', 'symbol' => 'د.ك', 'flag' => '🇰🇼', 'country' => 'Kuwait'],
            'LBP' => ['name' => 'Lebanese Pound', 'symbol' => 'ل.ل', 'flag' => '🇱🇧', 'country' => 'Lebanon'],
            'LYD' => ['name' => 'Libyan Dinar', 'symbol' => 'ل.د', 'flag' => '🇱🇾', 'country' => 'Libya'],
            'MRU' => ['name' => 'Mauritanian Ouguiya', 'symbol' => 'أ.م', 'flag' => '🇲🇷', 'country' => 'Mauritania'],
            'MAD' => ['name' => 'Moroccan Dirham', 'symbol' => 'د.م.', 'flag' => '🇲🇦', 'country' => 'Morocco'],
            'OMR' => ['name' => 'Omani Rial', 'symbol' => 'ر.ع.', 'flag' => '🇴🇲', 'country' => 'Oman'],
            'QAR' => ['name' => 'Qatari Riyal', 'symbol' => 'ر.ق', 'flag' => '🇶🇦', 'country' => 'Qatar'],
            'SAR' => ['name' => 'Saudi Riyal', 'symbol' => 'ر.س', 'flag' => '🇸🇦', 'country' => 'Saudi Arabia'],
            'SOS' => ['name' => 'Somali Shilling', 'symbol' => 'S', 'flag' => '🇸🇴', 'country' => 'Somalia'],
            'SDG' => ['name' => 'Sudanese Pound', 'symbol' => 'ج.س.', 'flag' => '🇸🇩', 'country' => 'Sudan'],
            'SYP' => ['name' => 'Syrian Pound', 'symbol' => 'ل.س', 'flag' => '🇸🇾', 'country' => 'Syria'],
            'TND' => ['name' => 'Tunisian Dinar', 'symbol' => 'د.ت', 'flag' => '🇹🇳', 'country' => 'Tunisia'],
            'AED' => ['name' => 'UAE Dirham', 'symbol' => 'د.إ', 'flag' => '🇦🇪', 'country' => 'United Arab Emirates'],
            'YER' => ['name' => 'Yemeni Rial', 'symbol' => 'ر.ي', 'flag' => '🇾🇪', 'country' => 'Yemen'],
            
            // Other common currencies
            'USD' => ['name' => 'US Dollar', 'symbol' => '$', 'flag' => '🇺🇸', 'country' => 'United States'],
            'EUR' => ['name' => 'Euro', 'symbol' => '€', 'flag' => '🇪🇺', 'country' => 'European Union'],
            'GBP' => ['name' => 'British Pound', 'symbol' => '£', 'flag' => '🇬🇧', 'country' => 'United Kingdom'],
            'JPY' => ['name' => 'Japanese Yen', 'symbol' => '¥', 'flag' => '🇯🇵', 'country' => 'Japan'],
            'CAD' => ['name' => 'Canadian Dollar', 'symbol' => 'C$', 'flag' => '🇨🇦', 'country' => 'Canada'],
            'AUD' => ['name' => 'Australian Dollar', 'symbol' => 'A$', 'flag' => '🇦🇺', 'country' => 'Australia'],
            'CHF' => ['name' => 'Swiss Franc', 'symbol' => 'Fr', 'flag' => '🇨🇭', 'country' => 'Switzerland'],
            'CNY' => ['name' => 'Chinese Yuan', 'symbol' => '¥', 'flag' => '🇨🇳', 'country' => 'China'],
            'INR' => ['name' => 'Indian Rupee', 'symbol' => '₹', 'flag' => '🇮🇳', 'country' => 'India'],
        ];
    }

    /**
     * Get all Arabic countries' currencies.
     *
     * @return array
     */
    public static function getArabicCurrencies(): array
    {
        $allCurrencies = self::getAllCurrencies();
        $arabicCountries = [
            'DZD', 'BHD', 'KMF', 'DJF', 'EGP', 'IQD', 'JOD', 'KWD', 'LBP', 
            'LYD', 'MRU', 'MAD', 'OMR', 'QAR', 'SAR', 'SOS', 'SDG', 'SYP', 
            'TND', 'AED', 'YER'
        ];
        
        return array_filter($allCurrencies, function($key) use ($arabicCountries) {
            return in_array($key, $arabicCountries);
        }, ARRAY_FILTER_USE_KEY);
    }

    /**
     * Format a currency value with its symbol.
     *
     * @param string $currencyCode
     * @param float $amount
     * @return string
     */
    public static function formatCurrency(string $currencyCode, float $amount): string
    {
        $currencies = self::getAllCurrencies();
        
        if (isset($currencies[$currencyCode])) {
            return $currencies[$currencyCode]['symbol'] . ' ' . number_format($amount, 2);
        }
        
        return $currencyCode . ' ' . number_format($amount, 2);
    }

    /**
     * Get currency flag emoji.
     *
     * @param string $currencyCode
     * @return string
     */
    public static function getCurrencyFlag(string $currencyCode): string
    {
        $currencies = self::getAllCurrencies();
        
        if (isset($currencies[$currencyCode]) && isset($currencies[$currencyCode]['flag'])) {
            return $currencies[$currencyCode]['flag'];
        }
        
        return '';
    }
} 