<?php

namespace App\Notifications;

use App\Models\Family;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class FamilyInvitationNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The family instance.
     *
     * @var Family
     */
    protected $family;

    /**
     * The user who sent the invitation.
     *
     * @var User
     */
    protected $inviter;

    /**
     * The invitation token.
     *
     * @var string
     */
    protected $token;

    /**
     * Create a new notification instance.
     */
    public function __construct(Family $family, User $inviter, string $token)
    {
        $this->family = $family;
        $this->inviter = $inviter;
        $this->token = $token;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject(__('families.invitation_subject', ['family' => $this->family->name]))
            ->greeting(__('families.invitation_greeting', ['name' => $notifiable->name]))
            ->line(__('families.invitation_intro', [
                'inviter' => $this->inviter->name,
                'family' => $this->family->name
            ]))
            ->action(__('families.accept_invitation'), url('/families/invitations/' . $this->token))
            ->line(__('families.invitation_expires'))
            ->line(__('families.invitation_ignore'));
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'family_id' => $this->family->id,
            'family_name' => $this->family->name,
            'inviter_id' => $this->inviter->id,
            'inviter_name' => $this->inviter->name,
            'token' => $this->token,
            'type' => 'family_invitation'
        ];
    }
}
