<?php

namespace App\Providers;

use App\Contracts\AccountRepositoryInterface;
use App\Models\Account;
use App\Models\Family;
use App\Models\Transaction;
use App\Models\User;
use App\Repositories\AccountRepository;
use App\Repositories\FamilyRepository;
use App\Repositories\TransactionRepository;
use App\Repositories\UserRepository;
use Illuminate\Support\ServiceProvider;

class RepositoryServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(AccountRepositoryInterface::class, AccountRepository::class);
        
        $this->app->when(AccountRepository::class)
            ->needs(Account::class)
            ->give(function () {
                return new Account();
            });
            
        $this->app->singleton(FamilyRepository::class, function () {
            return new FamilyRepository();
        });
        
        $this->app->singleton(TransactionRepository::class, function () {
            return new TransactionRepository();
        });
        
        $this->app->singleton(UserRepository::class, function () {
            return new UserRepository();
        });
    }
} 