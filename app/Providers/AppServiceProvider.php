<?php

namespace App\Providers;

use App\Helpers\CurrencyHelper;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\URL;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // Force HTTPS in production
        if($this->app->environment('production')) {
            URL::forceScheme('https');
        }
        
        // Share navigation data with all views
        View::composer('*', function ($view) {
            $view->with('previousUrl', url()->previous());
            
            // Share all currencies and Arabic currencies with views
            $view->with('allCurrencies', CurrencyHelper::getAllCurrencies());
            $view->with('arabicCurrencies', CurrencyHelper::getArabicCurrencies());
        });
        
        // Register error handlers that include navigation buttons
        $this->registerErrorHandlers();
    }
    
    /**
     * Register custom error handlers
     */
    protected function registerErrorHandlers()
    {
        // Override Laravel's error views with our custom ones
        $this->app->singleton(
            \Illuminate\Contracts\Debug\ExceptionHandler::class,
            \App\Exceptions\Handler::class
        );
    }
}
