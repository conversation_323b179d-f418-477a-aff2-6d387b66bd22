<?php declare(strict_types=1);

namespace App\Enums\Families;

use BenSampo\Enum\Enum;

/**
 * Family relationship types for members within a family
 * 
 * @method static static SPOUSE()
 * @method static static CHILD()
 * @method static static PARENT()
 * @method static static SIBLING()
 * @method static static GRANDPARENT()
 * @method static static GRANDCHILD()
 * @method static static AUNT_UNCLE()
 * @method static static NIECE_NEPHEW()
 * @method static static COUSIN()
 * @method static static IN_LAW()
 * @method static static STEP_RELATION()
 * @method static static FRIEND()
 * @method static static ROOMMATE()
 * @method static static FAMILY()
 * @method static static OTHER()
 */
final class FamilyRelationships extends Enum
{
    // Primary family relationships
    const SPOUSE = 'spouse';
    const CHILD = 'child';
    const PARENT = 'parent';
    const SIBLING = 'sibling';
    
    // Extended family relationships
    const GRANDPARENT = 'grandparent';
    const GRANDCHILD = 'grandchild';
    const AUNT_UNCLE = 'aunt_uncle';
    const NIECE_NEPHEW = 'niece_nephew';
    const COUSIN = 'cousin';
    const IN_LAW = 'in_law';
    const STEP_RELATION = 'step_relation';
    
    // Non-family relationships
    const FRIEND = 'friend';
    const ROOMMATE = 'roommate';
    
    // Generic relationships
    const FAMILY = 'family'; // Generic family member
    const OTHER = 'other';
    
    /**
     * Get the translated value for the relationship type
     *
     * @return string
     */
    public function getTranslation(): string
    {
        return __('families.relationship_types.' . $this->value);
    }
    
    /**
     * Get all relationship types with their translations
     *
     * @return array
     */
    public static function getTranslatedOptions(): array
    {
        $options = [];
        
        foreach (self::getValues() as $value) {
            $options[$value] = __('families.relationship_types.' . $value);
        }
        
        return $options;
    }
    
    /**
     * Group relationship types by category with translations
     *
     * @return array
     */
    public static function getGroupedOptions(): array
    {
        return [
            'primary' => [
                'label' => __('families.relationship_categories.primary'),
                'options' => [
                    self::SPOUSE => __('families.relationship_types.' . self::SPOUSE),
                    self::CHILD => __('families.relationship_types.' . self::CHILD),
                    self::PARENT => __('families.relationship_types.' . self::PARENT),
                    self::SIBLING => __('families.relationship_types.' . self::SIBLING),
                ],
            ],
            'extended' => [
                'label' => __('families.relationship_categories.extended'),
                'options' => [
                    self::GRANDPARENT => __('families.relationship_types.' . self::GRANDPARENT),
                    self::GRANDCHILD => __('families.relationship_types.' . self::GRANDCHILD),
                    self::AUNT_UNCLE => __('families.relationship_types.' . self::AUNT_UNCLE),
                    self::NIECE_NEPHEW => __('families.relationship_types.' . self::NIECE_NEPHEW),
                    self::COUSIN => __('families.relationship_types.' . self::COUSIN),
                    self::IN_LAW => __('families.relationship_types.' . self::IN_LAW),
                    self::STEP_RELATION => __('families.relationship_types.' . self::STEP_RELATION),
                ],
            ],
            'non_family' => [
                'label' => __('families.relationship_categories.non_family'),
                'options' => [
                    self::FRIEND => __('families.relationship_types.' . self::FRIEND),
                    self::ROOMMATE => __('families.relationship_types.' . self::ROOMMATE),
                ],
            ],
            'generic' => [
                'label' => __('families.relationship_categories.generic'),
                'options' => [
                    self::FAMILY => __('families.relationship_types.' . self::FAMILY),
                    self::OTHER => __('families.relationship_types.' . self::OTHER),
                ],
            ],
        ];
    }
}
