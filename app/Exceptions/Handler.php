<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Request;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }
    
    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $e
     * @return \Symfony\Component\HttpFoundation\Response
     *
     * @throws \Throwable
     */
    public function render($request, Throwable $e)
    {
        // Return JSON for API requests
        if ($request->expectsJson()) {
            return $this->handleApiException($request, $e);
        }
        
        $response = parent::render($request, $e);
        
        // Store the error message in the session for flash messages
        if (!$e instanceof \Illuminate\Session\TokenMismatchException) {
            session()->flash('error', $e->getMessage());
            
            // Store additional error context to help with recovery
            $errorContext = $this->getErrorContext($e);
            if ($errorContext) {
                session()->flash('error_context', $errorContext);
            }
            
            // Store error code and current URL for reference
            session()->flash('error_code', $this->getExceptionStatusCode($e));
            session()->flash('error_url', $request->fullUrl());
        }
        
        return $response;
    }
    
    /**
     * Get additional context about the error to help users understand what happened
     */
    private function getErrorContext(Throwable $exception): ?string
    {
        $context = null;
        
        // Add specific context based on exception type
        if ($exception instanceof \Illuminate\Database\QueryException) {
            $context = __('There was a problem with the database operation.');
        } elseif ($exception instanceof \Illuminate\Validation\ValidationException) {
            $context = __('The submitted form contains validation errors.');
        } elseif ($exception instanceof \Illuminate\Auth\Access\AuthorizationException) {
            $context = __('You do not have permission to perform this action.');
        } elseif ($exception instanceof \Illuminate\Database\Eloquent\ModelNotFoundException) {
            $context = __('The requested record could not be found.');
        } elseif ($exception instanceof \Symfony\Component\HttpKernel\Exception\NotFoundHttpException) {
            $context = __('The requested page could not be found.');
        } elseif ($exception instanceof \Illuminate\Session\TokenMismatchException) {
            $context = __('Your session has expired. Please try again.');
        } elseif ($exception instanceof \Exception) {
            // Get the line and file for developers
            if (config('app.debug')) {
                $file = basename($exception->getFile());
                $line = $exception->getLine();
                $context = __('Error in :file on line :line', ['file' => $file, 'line' => $line]);
            } else {
                $context = __('An unexpected error occurred.');
            }
        }
        
        return $context;
    }
    
    /**
     * Handle API exceptions and return JSON responses
     */
    private function handleApiException(Request $request, Throwable $exception)
    {
        $statusCode = $this->getExceptionStatusCode($exception);
        
        return response()->json([
            'message' => $exception->getMessage(),
            'status_code' => $statusCode,
            'previous_url' => url()->previous(),
            'home_url' => route('dashboard'),
        ], $statusCode);
    }
    
    /**
     * Get the status code from the exception
     */
    private function getExceptionStatusCode(Throwable $exception): int
    {
        if (method_exists($exception, 'getStatusCode')) {
            return $exception->getStatusCode();
        }
        
        if ($exception instanceof \Illuminate\Auth\AuthenticationException) {
            return 401;
        }
        
        if ($exception instanceof \Illuminate\Auth\Access\AuthorizationException) {
            return 403;
        }
        
        if ($exception instanceof \Illuminate\Database\Eloquent\ModelNotFoundException) {
            return 404;
        }
        
        if ($exception instanceof \Symfony\Component\HttpKernel\Exception\NotFoundHttpException) {
            return 404;
        }
        
        if ($exception instanceof \Illuminate\Session\TokenMismatchException) {
            return 419;
        }
        
        return 500;
    }
} 