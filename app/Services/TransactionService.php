<?php

namespace App\Services;

use App\Models\Account;
use App\Models\Transaction;
use App\Models\User;
use App\Repositories\TransactionRepository;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;

class TransactionService
{
    protected TransactionRepository $transactionRepository;
    
    public function __construct(TransactionRepository $transactionRepository)
    {
        $this->transactionRepository = $transactionRepository;
    }
    
    public function getAccountTransactions(
        Account $account, 
        array $filters = [], 
        int $perPage = 15
    ): LengthAwarePaginator {
        return $this->transactionRepository->getAccountTransactions($account, $filters, $perPage);
    }
    
    public function getUserTransactions(?User $user = null, int $perPage = 15): LengthAwarePaginator
    {
        $user = $user ?? Auth::user();
        return $this->transactionRepository->getUserTransactions($user, $perPage);
    }
    
    public function createTransaction(array $data, Account $account, ?User $user = null): Transaction
    {
        $user = $user ?? Auth::user();
        return $this->transactionRepository->createTransaction($data, $account, $user);
    }
    
    public function updateTransaction(Transaction $transaction, array $data): Transaction
    {
        return $this->transactionRepository->updateTransaction($transaction, $data);
    }
    
    public function deleteTransaction(Transaction $transaction): bool
    {
        return $this->transactionRepository->deleteTransaction($transaction);
    }
    
    public function getMonthlyStats(Account $account, int $year = null): array
    {
        $year = $year ?? date('Y');
        return $this->transactionRepository->getTransactionStatsByMonth($account, $year);
    }
    
    public function getAccountBalance(Account $account): float
    {
        return $account->balance;
    }
    
    public function getSpendingByCategory(Account $account, ?string $period = null): array
    {
        $query = $account->transactions()->where('type', 'expense');
        
        if ($period === 'month') {
            $query->whereYear('date', date('Y'))
                  ->whereMonth('date', date('m'));
        } elseif ($period === 'year') {
            $query->whereYear('date', date('Y'));
        }
        
        $results = $query->selectRaw('category, SUM(amount) as total')
            ->groupBy('category')
            ->orderByDesc('total')
            ->get();
            
        return $results->pluck('total', 'category')->toArray();
    }
} 