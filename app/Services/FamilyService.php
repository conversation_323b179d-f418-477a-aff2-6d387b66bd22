<?php

namespace App\Services;

use App\Models\Account;
use App\Models\Family;
use App\Models\User;
use App\Repositories\FamilyRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;

class FamilyService
{
    protected FamilyRepository $familyRepository;
    
    public function __construct(FamilyRepository $familyRepository)
    {
        $this->familyRepository = $familyRepository;
    }
    
    public function getOwnedFamilies(?User $user = null, array $with = []): Collection
    {
        $user = $user ?? Auth::user();
        return $this->familyRepository->getOwnedFamilies($user, $with);
    }
    
    public function getFamiliesMemberOf(?User $user = null, array $with = []): Collection
    {
        $user = $user ?? Auth::user();
        return $this->familyRepository->getFamiliesMemberOf($user, $with);
    }
    
    public function getAllFamilies(?User $user = null): Collection
    {
        $user = $user ?? Auth::user();
        $owned = $this->getOwnedFamilies($user);
        $memberOf = $this->getFamiliesMemberOf($user);
        
        // Merge and deduplicate
        return $owned->merge($memberOf)->unique('id')->values();
    }
    
    public function createFamily(array $data, ?User $owner = null): Family
    {
        $owner = $owner ?? Auth::user();
        
        $family = new Family();
        $family->name = $data['name'];
        $family->description = $data['description'] ?? null;
        $family->owner_id = $owner->id;
        $family->save();
        
        // Add owner as a family member
        $this->familyRepository->addMemberToFamily($family, $owner);
        
        return $family;
    }
    
    public function updateFamily(Family $family, array $data): Family
    {
        return $this->familyRepository->updateFamily($family->id, $data);
    }
    
    public function deleteFamily(Family $family): bool
    {
        return $this->familyRepository->deleteFamily($family->id);
    }
    
    public function addMember(Family $family, User $user): void
    {
        $this->familyRepository->addMemberToFamily($family, $user);
    }
    
    public function removeMember(Family $family, User $user): void
    {
        $this->familyRepository->removeMemberFromFamily($family, $user);
    }
    
    public function shareAccountWithFamily(Family $family, Account $account, string $permission = 'read'): void
    {
        $family->accounts()->syncWithoutDetaching([
            $account->id => ['permission' => $permission]
        ]);
    }
    
    public function removeAccountFromFamily(Family $family, Account $account): void
    {
        $family->accounts()->detach($account->id);
    }
    
    public function getFamilyMembers(Family $family): Collection
    {
        return $this->familyRepository->getFamilyMembers($family);
    }
    
    public function getFamilyAccounts(Family $family): Collection
    {
        return $this->familyRepository->getFamilyAccounts($family);
    }
} 