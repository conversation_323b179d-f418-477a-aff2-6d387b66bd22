<?php

namespace App\Services;

use App\Models\User;
use App\Repositories\AccountRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;

class DashboardService
{
    protected AccountRepository $accountRepository;

    public function __construct(AccountRepository $accountRepository)
    {
        $this->accountRepository = $accountRepository;
    }

    public function getDashboardData(?User $user = null): array
    {
        $user = $user ?? Auth::user();
        
        if (!$user) {
            return [];
        }
        
        return [
            'ownedAccounts' => $this->accountRepository->getOwnedAccounts($user, [], ['transactions']),
            'sharedAccounts' => $this->accountRepository->getSharedAccounts($user, [], ['transactions']),
            'families' => $user->families()->with('accounts')->get(),
            'recentTransactions' => $this->accountRepository->getRecentTransactions($user, 10),
        ];
    }
    
    public function getOwnedAccounts(?User $user = null, array $with = [], array $withCount = []): Collection
    {
        $user = $user ?? Auth::user();
        return $this->accountRepository->getOwnedAccounts($user, $with, $withCount);
    }
    
    public function getSharedAccounts(?User $user = null, array $with = [], array $withCount = []): Collection
    {
        $user = $user ?? Auth::user();
        return $this->accountRepository->getSharedAccounts($user, $with, $withCount);
    }
    
    public function getRecentTransactions(?User $user = null, int $limit = 10): Collection
    {
        $user = $user ?? Auth::user();
        return $this->accountRepository->getRecentTransactions($user, $limit);
    }
}