<?php

namespace App\Services;

use App\Models\User;
use App\Repositories\UserRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;

class UserService
{
    protected UserRepository $userRepository;
    
    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }
    
    public function findByMobileNumber(string $mobileNumber): ?User
    {
        return $this->userRepository->findByMobileNumber($mobileNumber);
    }
    
    public function searchUsers(string $query, int $perPage = 10): LengthAwarePaginator
    {
        return $this->userRepository->searchUsers($query, $perPage);
    }
    
    public function getFamilyMembers(User $user): Collection
    {
        return $this->userRepository->getFamilyMembers($user);
    }
    
    public function updateProfile(User $user, array $data): User
    {
        return $this->userRepository->updateUser($user->id, $data);
    }
    
    public function updatePassword(User $user, string $newPassword): bool
    {
        return $this->userRepository->updatePassword($user->id, $newPassword);
    }
    
    public function getUsersWithRole(string $role): Collection
    {
        return $this->userRepository->getUsersWithRole($role);
    }
    
    public function updateUserRole(User $user, string $role): void
    {
        $this->userRepository->updateUserRole($user, $role);
    }
} 