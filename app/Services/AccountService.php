<?php

namespace App\Services;

use App\Models\Account;
use App\Models\User;
use App\Repositories\AccountRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;

class AccountService
{
    protected AccountRepository $accountRepository;
    
    public function __construct(AccountRepository $accountRepository)
    {
        $this->accountRepository = $accountRepository;
    }
    
    public function createAccount(array $data, ?User $owner = null): Account
    {
        $owner = $owner ?? Auth::user();
        
        $data['owner_id'] = $owner->id;
        
        return $this->accountRepository->create($data);
    }
    
    public function updateAccount(Account $account, array $data): Account
    {
        return $this->accountRepository->update($account->id, $data);
    }
    
    public function deleteAccount(Account $account): bool
    {
        return $this->accountRepository->delete($account->id);
    }
    
    public function shareWithUser(Account $account, User $user, string $permission = 'read'): void
    {
        $account->users()->syncWithoutDetaching([
            $user->id => ['permission' => $permission]
        ]);
    }
    
    public function removeUserAccess(Account $account, User $user): void
    {
        $account->users()->detach($user->id);
    }
    
    /**
     * Get accounts owned by a user
     */
    public function getOwnedAccounts(?User $user = null, array $with = [], array $withCount = ['transactions']): Collection
    {
        $user = $user ?? Auth::user();
        return $this->accountRepository->getOwnedAccounts($user, $with, $withCount);
    }
    
    /**
     * Get accounts shared with a user
     */
    public function getSharedAccounts(?User $user = null, array $with = [], array $withCount = ['transactions']): Collection
    {
        $user = $user ?? Auth::user();
        return $this->accountRepository->getSharedAccounts($user, $with, $withCount);
    }
    
    public function getAccountsWithPositiveBalance(): Collection
    {
        return $this->accountRepository->findBy(['balance' => ['operator' => '>', 'value' => 0]]);
    }
    
    public function getAccountsWithNegativeBalance(): Collection
    {
        return $this->accountRepository->findBy(['balance' => ['operator' => '<', 'value' => 0]]);
    }
    
    public function getAccountsOrderedByTransactionCount(string $direction = 'desc'): Collection
    {
        return $this->accountRepository->getAccountsOrderedBy('transactions_count', $direction);
    }
    
    public function getAccountsOrderedByRecentActivity(): Collection
    {
        return $this->accountRepository->getAccountsWithRecentActivity();
    }
}