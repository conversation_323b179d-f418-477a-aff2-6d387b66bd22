<?php

namespace App\Policies;

use App\Models\Account;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class AccountPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true; // All authenticated users can view accounts list
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Account $account): bool
    {
        // User can view if they are the owner
        if ($user->id === $account->owner_id) {
            return true;
        }
        
        // User can view if they have access through account_user
        if ($account->users()->where('user_id', $user->id)->exists()) {
            return true;
        }
        
        // User can view if they belong to a family that has access
        return $user->families()
            ->whereHas('accounts', function ($query) use ($account) {
                $query->where('accounts.id', $account->id);
            })
            ->exists();
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return true; // All authenticated users can create accounts
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Account $account): bool
    {
        // Only the owner can update account details
        if ($user->id === $account->owner_id) {
            return true;
        }
        
        // User can update if they have edit permission through account_user
        if ($account->users()
            ->where('user_id', $user->id)
            ->where('permission', 'edit')
            ->exists()) {
            return true;
        }
        
        // User can update if they belong to a family that has edit access
        return $user->families()
            ->whereHas('accounts', function ($query) use ($account) {
                $query->where('accounts.id', $account->id)
                    ->where('permission', 'edit');
            })
            ->exists();
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Account $account): bool
    {
        // Only the owner can delete an account
        return $user->id === $account->owner_id;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Account $account): bool
    {
        // Only the owner can restore an account
        return $user->id === $account->owner_id;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Account $account): bool
    {
        // Only the owner can permanently delete an account
        return $user->id === $account->owner_id;
    }
}
