<?php

namespace App\Policies;

use App\Models\Transaction;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class TransactionPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Transaction $transaction): bool
    {
        // Can view if user can view the associated account
        return $user->can('view', $transaction->account);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Transaction $transaction): bool
    {
        // User can update if:
        // 1. They are the owner of the account
        if ($user->id === $transaction->account->owner_id) {
            return true;
        }
        
        // 2. They created the transaction AND they still have edit permission on the account
        if ($user->id === $transaction->user_id) {
            return $user->can('update', $transaction->account);
        }
        
        // 3. They have edit permission on the account
        return $user->can('update', $transaction->account);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Transaction $transaction): bool
    {
        // Only the owner of the account or the creator of the transaction can delete it
        return $user->id === $transaction->account->owner_id || 
               ($user->id === $transaction->user_id && $user->can('update', $transaction->account));
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Transaction $transaction): bool
    {
        // Only the owner of the account can restore
        return $user->id === $transaction->account->owner_id;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Transaction $transaction): bool
    {
        // Only the owner of the account can permanently delete
        return $user->id === $transaction->account->owner_id;
    }
}
