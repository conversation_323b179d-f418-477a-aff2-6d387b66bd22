<?php

namespace App\Contracts;

use App\Models\User;
use Illuminate\Database\Eloquent\Collection;

interface AccountRepositoryInterface extends RepositoryInterface
{
    public function getOwnedAccounts(User $user, array $with = [], array $withCount = []): Collection;
    
    public function getSharedAccounts(User $user, array $with = [], array $withCount = []): Collection;
    
    public function getRecentTransactions(User $user, int $limit = 10): Collection;
} 