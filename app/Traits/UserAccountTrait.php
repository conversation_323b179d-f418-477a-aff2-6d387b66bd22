<?php

namespace App\Traits;

use App\Models\Account;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait UserAccountTrait 
{
    public function getOwnedAccountsWithDetails(array $with = [], array $withCount = []): Collection
    {
        $query = $this->ownedAccounts();
        
        if (!empty($with)) {
            $query->with($with);
        }
        
        if (!empty($withCount)) {
            $query->withCount($withCount);
        }
        
        return $query->orderBy('name')->get();
    }
    
    public function getSharedAccountsWithDetails(array $with = [], array $withCount = []): Collection
    {
        $query = $this->accounts();
        
        if (!empty($with)) {
            $query->with($with);
        }
        
        if (!empty($withCount)) {
            $query->withCount($withCount);
        }
        
        return $query->orderBy('name')->get();
    }
    
    public function getAllAccessibleAccounts(): Collection
    {
        $ownedIds = $this->ownedAccounts()->pluck('id');
        $sharedIds = $this->accounts()->pluck('accounts.id');
        
        return Account::whereIn('id', $ownedIds->merge($sharedIds))->get();
    }
    
    public function getRecentTransactions(int $limit = 10): Collection
    {
        return \App\Models\Transaction::whereIn('account_id', function ($query) {
                $query->select('id')
                    ->from('accounts')
                    ->where('owner_id', $this->id)
                    ->union(
                        $query->newQuery()
                            ->select('account_id')
                            ->from('account_user')
                            ->where('user_id', $this->id)
                    );
            })
            ->with(['account', 'user'])
            ->latest()
            ->take($limit)
            ->get();
    }
}