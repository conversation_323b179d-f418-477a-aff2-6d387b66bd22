<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;

trait TransactionQueryTrait
{
    public function scopeIncome(Builder $query): Builder
    {
        return $query->where('type', 'income');
    }
    
    public function scopeExpense(Builder $query): Builder
    {
        return $query->where('type', 'expense');
    }
    
    public function scopeByMonth(Builder $query, int $month, ?int $year = null): Builder
    {
        $year = $year ?? date('Y');
        
        return $query->whereYear('date', $year)
            ->whereMonth('date', $month);
    }
    
    public function scopeByYear(Builder $query, int $year): Builder
    {
        return $query->whereYear('date', $year);
    }
    
    public function scopeByCategory(Builder $query, string $category): Builder
    {
        return $query->where('category', $category);
    }
    
    public function scopeByDateRange(Builder $query, string $startDate, string $endDate): Builder
    {
        return $query->whereDate('date', '>=', $startDate)
            ->whereDate('date', '<=', $endDate);
    }
    
    public function scopeRecent(Builder $query, int $days = 30): Builder
    {
        return $query->whereDate('date', '>=', now()->subDays($days)->toDateString());
    }
    
    public function scopeSearch(Builder $query, string $term): Builder
    {
        return $query->where(function($q) use ($term) {
            $q->where('description', 'like', '%' . $term . '%')
              ->orWhere('category', 'like', '%' . $term . '%');
        });
    }
    
    public function scopeOrderByLatest(Builder $query): Builder
    {
        return $query->latest('date');
    }
    
    public function scopeOrderByAmount(Builder $query, string $direction = 'desc'): Builder
    {
        return $query->orderBy('amount', $direction);
    }
} 