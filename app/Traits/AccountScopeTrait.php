<?php

namespace App\Traits;

trait AccountScopeTrait
{
    public function scopePositiveBalance($query)
    {
        return $query->where('balance', '>', 0);
    }
    
    public function scopeNegativeBalance($query)
    {
        return $query->where('balance', '<', 0);
    }
    
    public function scopeZeroBalance($query)
    {
        return $query->where('balance', 0);
    }
    
    public function scopeOrderByBalanceDesc($query)
    {
        return $query->orderBy('balance', 'desc');
    }
    
    public function scopeOrderByBalanceAsc($query)
    {
        return $query->orderBy('balance', 'asc');
    }
    
    public function scopeOrderByTransactionCount($query, $direction = 'desc')
    {
        return $query->withCount('transactions')
            ->orderBy('transactions_count', $direction);
    }
    
    public function scopeOrderByRecentActivity($query)
    {
        return $query->addSelect(['latest_transaction' => function ($subquery) {
                $subquery->select('created_at')
                    ->from('transactions')
                    ->whereColumn('account_id', 'accounts.id')
                    ->latest()
                    ->limit(1);
            }])
            ->orderByDesc('latest_transaction');
    }
} 