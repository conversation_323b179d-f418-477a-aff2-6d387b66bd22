<?php

namespace App\Exports;

use App\Models\Account;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class TransactionsExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $account;
    
    public function __construct(Account $account)
    {
        $this->account = $account;
    }
    
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->account->transactions()
            ->with('user')
            ->latest('transaction_date')
            ->get();
    }
    
    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'Date',
            'Description',
            'Type',
            'Category',
            'Amount',
            'Created By',
            'Created At',
        ];
    }
    
    /**
     * @param mixed $row
     * @return array
     */
    public function map($transaction): array
    {
        return [
            $transaction->id,
            $transaction->transaction_date->format('Y-m-d'),
            $transaction->description,
            ucfirst($transaction->type),
            $transaction->category ?? 'N/A',
            ($transaction->type === 'income' ? '+' : '-') . $this->account->currency . ' ' . number_format($transaction->amount, 2),
            $transaction->user->name,
            $transaction->created_at->format('Y-m-d H:i:s'),
        ];
    }
    
    /**
     * @param Worksheet $sheet
     */
    public function styles(Worksheet $sheet)
    {
        // Style the header row
        $sheet->getStyle('A1:H1')->applyFromArray([
            'font' => [
                'bold' => true,
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => [
                    'argb' => 'FFCCCCCC',
                ],
            ],
        ]);
        
        // Add account summary at the top
        $sheet->insertNewRowBefore(1, 5);
        
        $sheet->setCellValue('A1', 'Account Report: ' . $this->account->name);
        $sheet->setCellValue('A2', 'Total Income: ' . $this->account->currency . ' ' . number_format($this->account->total_income, 2));
        $sheet->setCellValue('A3', 'Total Expenses: ' . $this->account->currency . ' ' . number_format($this->account->total_expenses, 2));
        $sheet->setCellValue('A4', 'Current Balance: ' . $this->account->currency . ' ' . number_format($this->account->balance, 2));
        $sheet->setCellValue('A5', 'Generated on: ' . now()->format('Y-m-d H:i:s'));
        
        $sheet->getStyle('A1:A5')->applyFromArray([
            'font' => [
                'bold' => true,
            ],
        ]);
        
        return $sheet;
    }
} 