<?php

namespace App\Repositories;

use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Hash;

class UserRepository
{
    public function findByMobileNumber(string $mobileNumber): ?User
    {
        return User::allowSearchByMobile()
            ->where('mobile_number', $mobileNumber)
            ->first();
    }
    
    public function searchUsers(string $query, int $perPage = 10): LengthAwarePaginator
    {
        return User::where(function($q) use ($query) {
                $q->where('name', 'like', '%' . $query . '%')
                  ->orWhere('email', 'like', '%' . $query . '%')
                  ->orWhere('mobile_number', 'like', '%' . $query . '%');
            })
            ->orderBy('name')
            ->paginate($perPage);
    }
    
    public function getFamilyMembers(User $user): Collection
    {
        $familyIds = $user->families()->pluck('families.id');
        
        return User::whereHas('families', function($query) use ($familyIds) {
                $query->whereIn('families.id', $familyIds);
            })
            ->where('id', '!=', $user->id)
            ->orderBy('name')
            ->get();
    }
    
    public function updateSettings(User $user, array $settings): User
    {
        $user->update($settings);
        return $user;
    }
    
    public function getUsersWithRole(string $role): Collection
    {
        return User::role($role)->get();
    }
    
    public function updateUserRole(User $user, string $role): void
    {
        $user->syncRoles([$role]);
    }
    
    /**
     * Update user profile information
     */
    public function updateUser(int $userId, array $data): User
    {
        $user = User::findOrFail($userId);
        
        $user->name = $data['name'];
        $user->email = $data['email'];
        
        if (!empty($data['mobile_number'])) {
            $user->mobile_number = $data['mobile_number'];
        }
        
        if (isset($data['allow_search_by_mobile'])) {
            $user->allow_search_by_mobile = (bool)$data['allow_search_by_mobile'];
        }
        
        if (isset($data['family_invitation_mode'])) {
            $user->family_invitation_mode = $data['family_invitation_mode'];
        }
        
        $user->save();
        
        return $user;
    }
    
    /**
     * Update user password
     */
    public function updatePassword(int $userId, string $newPassword): bool
    {
        $user = User::findOrFail($userId);
        $user->password = Hash::make($newPassword);
        return $user->save();
    }
}
