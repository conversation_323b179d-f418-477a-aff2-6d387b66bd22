<?php

namespace App\Repositories;

use App\Models\Account;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class TransactionRepository
{
    public function getAccountTransactions(
        Account $account, 
        array $filters = [], 
        int $perPage = 15
    ): LengthAwarePaginator {
        $query = $account->transactions();
        
        if (isset($filters['type']) && in_array($filters['type'], ['income', 'expense'])) {
            $query->where('type', $filters['type']);
        }
        
        if (isset($filters['start_date'])) {
            $query->whereDate('date', '>=', $filters['start_date']);
        }
        
        if (isset($filters['end_date'])) {
            $query->whereDate('date', '<=', $filters['end_date']);
        }
        
        if (isset($filters['category'])) {
            $query->where('category', $filters['category']);
        }
        
        if (isset($filters['search'])) {
            $query->where(function($q) use ($filters) {
                $q->where('description', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('category', 'like', '%' . $filters['search'] . '%');
            });
        }
        
        return $query->with(['user'])->latest('date')->paginate($perPage);
    }
    
    public function getUserTransactions(User $user, int $perPage = 15): LengthAwarePaginator
    {
        return $user->transactions()
            ->with(['account'])
            ->latest('date')
            ->paginate($perPage);
    }
    
    public function createTransaction(array $data, Account $account, User $user): Transaction
    {
        $transaction = new Transaction($data);
        $transaction->account_id = $account->id;
        $transaction->user_id = $user->id;
        $transaction->save();
        
        // Update account balance
        $account->recalculateBalance();
        
        return $transaction;
    }
    
    public function updateTransaction(Transaction $transaction, array $data): Transaction
    {
        $transaction->update($data);
        
        // Update account balance
        $transaction->account->recalculateBalance();
        
        return $transaction;
    }
    
    public function deleteTransaction(Transaction $transaction): bool
    {
        $account = $transaction->account;
        $deleted = $transaction->delete();
        
        if ($deleted) {
            $account->recalculateBalance();
        }
        
        return $deleted;
    }
    
    public function getTransactionStatsByMonth(Account $account, int $year): array
    {
        $stats = [];
        
        for ($month = 1; $month <= 12; $month++) {
            $income = $account->transactions()
                ->where('type', 'income')
                ->whereYear('date', $year)
                ->whereMonth('date', $month)
                ->sum('amount');
            
            $expense = $account->transactions()
                ->where('type', 'expense')
                ->whereYear('date', $year)
                ->whereMonth('date', $month)
                ->sum('amount');
            
            $stats[$month] = [
                'income' => $income,
                'expense' => $expense,
                'net' => $income - $expense
            ];
        }
        
        return $stats;
    }
}
