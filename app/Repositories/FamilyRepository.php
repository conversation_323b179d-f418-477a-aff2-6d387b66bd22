<?php

namespace App\Repositories;

use App\Models\Family;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;

class FamilyRepository
{
    public function getOwnedFamilies(User $user, array $with = []): Collection
    {
        $query = $user->ownedFamilies();
        
        if (!empty($with)) {
            $query->with($with);
        }
        
        return $query->orderBy('name')->get();
    }
    
    public function getFamiliesMemberOf(User $user, array $with = []): Collection
    {
        $query = $user->families();
        
        if (!empty($with)) {
            $query->with($with);
        }
        
        return $query->orderBy('name')->get();
    }
    
    public function getFamilyDetails(Family $family, array $with = []): Family
    {
        if (!empty($with)) {
            $family->load($with);
        }
        
        return $family;
    }
    
    public function getFamilyMembers(Family $family): Collection
    {
        return $family->users()->orderBy('name')->get();
    }
    
    public function getFamilyAccounts(Family $family): Collection
    {
        return $family->accounts()->orderBy('name')->get();
    }
    
    public function addMemberToFamily(Family $family, User $user): void
    {
        if (!$family->users()->where('user_id', $user->id)->exists()) {
            $family->users()->attach($user->id);
        }
    }
    
    public function removeMemberFromFamily(Family $family, User $user): void
    {
        $family->users()->detach($user->id);
    }
    
    /**
     * Update a family by ID
     */
    public function updateFamily(int $id, array $data): Family
    {
        $family = Family::findOrFail($id);
        $family->update($data);
        return $family;
    }
    
    /**
     * Delete a family by ID
     */
    public function deleteFamily(int $id): bool
    {
        return Family::destroy($id) > 0;
    }
}
