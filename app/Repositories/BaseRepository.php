<?php

namespace App\Repositories;

use App\Contracts\RepositoryInterface;
use Illuminate\Database\Eloquent\Model;

abstract class BaseRepository implements RepositoryInterface
{
    protected $model;
    
    public function __construct(Model $model)
    {
        $this->model = $model;
    }
    
    public function all(array $columns = ['*'])
    {
        return $this->model->all($columns);
    }
    
    public function find(int $id, array $columns = ['*'])
    {
        return $this->model->findOrFail($id, $columns);
    }
    
    public function findBy(array $criteria, array $columns = ['*'])
    {
        $query = $this->model->query();
        
        foreach ($criteria as $key => $value) {
            $query->where($key, $value);
        }
        
        return $query->get($columns);
    }
    
    public function create(array $data)
    {
        return $this->model->create($data);
    }
    
    public function update(int $id, array $data)
    {
        $model = $this->find($id);
        $model->update($data);
        return $model;
    }
    
    public function delete(int $id)
    {
        return $this->model->destroy($id);
    }
} 