<?php

namespace App\Http\Rules;

class TransactionRules
{
    public static function sharedRules(): array
    {
        return [
            'account_id' => 'required|exists:accounts,id',
            'description' => 'required|string|max:255',
            'type' => 'required|in:income,expense',
            'amount' => 'required|numeric|min:0.01',
            'category' => 'required|string|max:50',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:50',
            'date' => 'required|date',
            'notes' => 'nullable|string|max:1000',
        ];
    }

    public static function sharedMessages(): array
    {
        return [
            'account_id.required' => 'Please select an account',
            'account_id.exists' => 'The selected account does not exist',
            'description.required' => 'Please provide a description for this transaction',
            'type.required' => 'Transaction type is required',
            'type.in' => 'Transaction type must be either income or expense',
            'amount.required' => 'Transaction amount is required',
            'amount.numeric' => 'Transaction amount must be a number',
            'amount.min' => 'Transaction amount must be at least 0.01',
            'category.required' => 'Please select a category',
            'tags.array' => 'Tags must be provided as an array',
            'date.required' => 'Transaction date is required',
            'date.date' => 'Please provide a valid date',
        ];
    }

    public static function sharedAttributes(): array
    {
        return [
            'account_id' => 'account',
            'description' => 'description',
            'type' => 'transaction type',
            'amount' => 'amount',
            'category' => 'category',
            'tags' => 'tags',
            'date' => 'date',
            'notes' => 'notes',
        ];
    }
} 