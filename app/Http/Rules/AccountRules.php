<?php

namespace App\Http\Rules;

class AccountRules
{
    public static function sharedRules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'currency' => 'required|string|size:3',
            'owner_id' => 'sometimes|exists:users,id',
        ];
    }

    public static function sharedMessages(): array
    {
        return [
            'name.required' => 'The account name is required',
            'name.max' => 'The account name cannot exceed 255 characters',
            'currency.required' => 'The currency is required',
            'currency.size' => 'The currency must be a 3-letter code (e.g., USD)',
            'owner_id.exists' => 'The selected owner does not exist',
        ];
    }

    public static function sharedAttributes(): array
    {
        return [
            'name' => 'account name',
            'description' => 'account description',
            'currency' => 'currency',
            'owner_id' => 'account owner',
        ];
    }
} 