<?php

namespace App\Http\Rules;

use Illuminate\Validation\Rules\Password;

class UserRules
{
    public static function sharedRules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'mobile_number' => 'nullable|string|max:20',
            'password' => ['sometimes', 'required', 'confirmed', Password::min(8)->letters()->numbers()->symbols()],
            'allow_search_by_mobile' => 'boolean',
            'family_invitation_mode' => 'string|in:anyone,approval,none',
        ];
    }

    public static function sharedMessages(): array
    {
        return [
            'name.required' => 'Your name is required',
            'email.required' => 'Your email address is required',
            'email.email' => 'Please provide a valid email address',
            'mobile_number.max' => 'Mobile number cannot exceed 20 characters',
            'password.required' => 'Password is required',
            'password.confirmed' => 'Password confirmation does not match',
            'family_invitation_mode.in' => 'Invalid family invitation mode',
        ];
    }

    public static function sharedAttributes(): array
    {
        return [
            'name' => 'name',
            'email' => 'email address',
            'mobile_number' => 'mobile number',
            'password' => 'password',
            'allow_search_by_mobile' => 'allow search by mobile',
            'family_invitation_mode' => 'family invitation mode',
        ];
    }
} 