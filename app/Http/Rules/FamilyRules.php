<?php

namespace App\Http\Rules;

class FamilyRules
{
    public static function sharedRules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'owner_id' => 'sometimes|exists:users,id',
            'members' => 'sometimes|array',
            'members.*' => 'exists:users,id',
        ];
    }

    public static function sharedMessages(): array
    {
        return [
            'name.required' => 'Family name is required',
            'name.max' => 'Family name cannot exceed 255 characters',
            'owner_id.exists' => 'The selected owner does not exist',
            'members.array' => 'Family members must be provided as an array',
            'members.*.exists' => 'One or more selected members do not exist',
        ];
    }

    public static function sharedAttributes(): array
    {
        return [
            'name' => 'family name',
            'description' => 'family description',
            'owner_id' => 'family owner',
            'members' => 'family members',
        ];
    }
} 