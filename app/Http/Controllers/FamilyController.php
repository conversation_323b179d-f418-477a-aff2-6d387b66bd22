<?php

namespace App\Http\Controllers;

use App\Models\Family;
use App\Models\User;
use App\Models\Account;
use Illuminate\Http\Request;

class FamilyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // Get all families where the authenticated user is the owner
        $ownedFamilies = auth()->user()->ownedFamilies;

        // Get all families where the authenticated user is a member
        $memberFamilies = auth()->user()->families;

        return view('families.index', compact('ownedFamilies', 'memberFamilies'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('families.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        $family = new Family();
        $family->name = $validated['name'];
        $family->description = $validated['description'] ?? null;
        $family->owner_id = auth()->id();
        $family->save();

        // Add the authenticated user as a family member
        $family->users()->attach(auth()->id());

        return redirect()->route('families.index')
            ->with('success', __('Family created successfully.'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Family $family)
    {
        // Check if the authenticated user is the owner or a member of the family
        if ($family->owner_id !== auth()->id() && !$family->users->contains(auth()->id())) {
            abort(403, 'Unauthorized access');
        }

        // Load the family's users and shared accounts
        $family->load(['users', 'accounts']);

        // Get list of users who are not members of this family for the add member form
        $nonMemberUsers = User::whereNotIn('id', $family->users->pluck('id'))->get();

        // Get list of accounts owned by the authenticated user that are not shared with this family
        $nonSharedAccounts = Account::where('owner_id', auth()->id())
            ->whereNotIn('id', $family->accounts->pluck('id'))
            ->get();

        return view('families.show', compact('family', 'nonMemberUsers', 'nonSharedAccounts'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Family $family)
    {
        // Check if the authenticated user is the owner of the family
        if ($family->owner_id !== auth()->id()) {
            abort(403, 'Only the family owner can edit the family details');
        }

        return view('families.edit', compact('family'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Family $family)
    {
        // Check if the authenticated user is the owner of the family
        if ($family->owner_id !== auth()->id()) {
            abort(403, 'Only the family owner can update the family details');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        $family->name = $validated['name'];
        $family->description = $validated['description'] ?? null;
        $family->save();

        return redirect()->route('families.index')
            ->with('success', __('Family updated successfully.'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Family $family)
    {
        // Check if the authenticated user is the owner of the family
        if ($family->owner_id !== auth()->id()) {
            abort(403, 'Only the family owner can delete the family');
        }

        // Delete the family (soft delete)
        $family->delete();

        return redirect()->route('families.index')
            ->with('success', __('Family removed successfully.'));
    }

    /**
     * Show the form for adding a member to the family.
     */
    public function addMemberForm(Family $family)
    {
        // Check if the authenticated user is the owner of the family
        if ($family->owner_id !== auth()->id()) {
            abort(403, 'Only the family owner can add members');
        }

        // Get users who are not members of this family
        $nonMemberUsers = User::whereNotIn('id', $family->users->pluck('id'))->get();

        return view('families.add-member', compact('family', 'nonMemberUsers'));
    }

    /**
     * Add a new member to the family.
     */
    public function addMember(Request $request, Family $family)
    {
        // Check if the authenticated user is the owner of the family
        if ($family->owner_id !== auth()->id()) {
            abort(403, 'Only the family owner can add members');
        }

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        // Check if the user is already a member
        if ($family->users->contains($validated['user_id'])) {
            return redirect()->route('families.show', $family)
                ->with('error', __('User is already a member of this family.'));
        }

        // Add the user to the family
        $family->users()->attach($validated['user_id']);

        return redirect()->route('families.show', $family)
            ->with('success', __('Member added successfully.'));
    }

    /**
     * Remove a member from the family.
     */
    public function removeMember(Request $request, Family $family, User $user)
    {
        // Check if the authenticated user is the owner of the family
        if ($family->owner_id !== auth()->id()) {
            abort(403, 'Only the family owner can remove members');
        }

        // Check if the user trying to be removed is the owner
        if ($user->id === $family->owner_id) {
            return redirect()->route('families.show', $family)
                ->with('error', __('Cannot remove the family owner.'));
        }

        // Remove the user from the family
        $family->users()->detach($user->id);

        return redirect()->route('families.show', $family)
            ->with('success', __('Member removed successfully.'));
    }

    /**
     * Show the form for sharing an account with the family.
     */
    public function shareAccountForm(Family $family)
    {
        // Check if the authenticated user is the owner of the family
        if ($family->owner_id !== auth()->id()) {
            abort(403, 'Only the family owner can share accounts');
        }

        // Get accounts owned by the authenticated user that are not shared with this family
        $nonSharedAccounts = Account::where('owner_id', auth()->id())
            ->whereNotIn('id', $family->accounts->pluck('id'))
            ->get();

        return view('families.share-account', compact('family', 'nonSharedAccounts'));
    }

    /**
     * Share an account with the family.
     */
    public function shareAccount(Request $request, Family $family)
    {
        // Check if the authenticated user is the owner of the family
        if ($family->owner_id !== auth()->id()) {
            abort(403, 'Only the family owner can share accounts');
        }

        $validated = $request->validate([
            'account_id' => 'required|exists:accounts,id',
            'permission' => 'required|in:view,edit',
        ]);

        // Check if the account is owned by the authenticated user
        $account = Account::findOrFail($validated['account_id']);
        if ($account->owner_id !== auth()->id()) {
            abort(403, 'You can only share accounts you own');
        }

        // Check if the account is already shared with this family
        if ($family->accounts->contains($validated['account_id'])) {
            return redirect()->route('families.show', $family)
                ->with('error', __('Account is already shared with this family.'));
        }

        // Share the account with the family
        $family->accounts()->attach($validated['account_id'], [
            'permission' => $validated['permission'],
        ]);

        return redirect()->route('families.show', $family)
            ->with('success', __('Account shared successfully.'));
    }

    /**
     * Unshare an account from the family.
     */
    public function unshareAccount(Family $family, Account $account)
    {
        // Check if the authenticated user is the owner of the family
        if ($family->owner_id !== auth()->id()) {
            abort(403, 'Only the family owner can unshare accounts');
        }

        // Unshare the account from the family
        $family->accounts()->detach($account->id);

        return redirect()->route('families.show', $family)
            ->with('success', __('Account unshared successfully.'));
    }
}
