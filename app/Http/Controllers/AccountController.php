<?php

namespace App\Http\Controllers;

use App\Http\Requests\AccountCreateRequest;
use App\Http\Requests\AccountShareRequest;
use App\Http\Requests\AccountUpdateRequest;
use App\Models\Account;
use App\Models\User;
use App\Services\AccountService;
use Illuminate\Http\Request;

class AccountController extends Controller
{
    protected AccountService $accountService;
    
    public function __construct(AccountService $accountService)
    {
        $this->accountService = $accountService;
        $this->middleware('auth');
    }
    
    public function index()
    {
        $ownedAccounts = $this->accountService->getOwnedAccounts();
        $sharedAccounts = $this->accountService->getSharedAccounts();
        
        // Merge the collections and paginate
        $allAccounts = $ownedAccounts->merge($sharedAccounts);
        $accounts = new \Illuminate\Pagination\LengthAwarePaginator(
            $allAccounts->forPage(\Illuminate\Pagination\Paginator::resolveCurrentPage(), 10),
            $allAccounts->count(),
            10,
            null,
            ['path' => \Illuminate\Pagination\Paginator::resolveCurrentPath()]
        );
        
        return view('accounts.index', compact('accounts'));
    }
    
    public function create()
    {
        return view('accounts.create');
    }
    
    public function store(AccountCreateRequest $request)
    {
        $account = $this->accountService->createAccount($request->validated());
        
        return redirect()->route('accounts.show', $account)
            ->with('success', 'Account created successfully');
    }
    
    public function show(Account $account)
    {
        $this->authorize('view', $account);
        
        return view('accounts.show', compact('account'));
    }
    
    public function edit(Account $account)
    {
        $this->authorize('update', $account);
        
        return view('accounts.edit', compact('account'));
    }
    
    public function update(AccountUpdateRequest $request, Account $account)
    {
        $this->accountService->updateAccount($account, $request->validated());
        
        return redirect()->route('accounts.show', $account)
            ->with('success', 'Account updated successfully');
    }
    
    public function destroy(Account $account)
    {
        $this->authorize('delete', $account);
        
        $this->accountService->deleteAccount($account);
        
        return redirect()->route('accounts.index')
            ->with('success', 'Account deleted successfully');
    }
    
    public function share(AccountShareRequest $request, Account $account)
    {
        $user = User::findOrFail($request->user_id);
        $permission = $request->permission;
        
        $this->accountService->shareWithUser($account, $user, $permission);
        
        return redirect()->route('accounts.show', $account)
            ->with('success', 'Account shared successfully');
    }
    
    public function removeUser(Request $request, Account $account, User $user)
    {
        $this->authorize('update', $account);
        
        $this->accountService->removeUserAccess($account, $user);
        
        return redirect()->route('accounts.show', $account)
            ->with('success', 'User access removed successfully');
    }
}
