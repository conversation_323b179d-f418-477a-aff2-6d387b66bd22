<?php

namespace App\Http\Controllers;

use App\Http\Requests\Transaction\CreateTransactionRequest;
use App\Http\Requests\Transaction\UpdateTransactionRequest;
use App\Models\Account;
use App\Models\Transaction;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class TransactionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Account $account): View
    {
        $this->authorize('view', $account);
        
        $transactions = $account->transactions()
            ->with('user')
            ->latest()
            ->paginate(15);
            
        return view('transactions.index', compact('account', 'transactions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Account $account): View
    {
        $this->authorize('update', $account);
        
        return view('transactions.create', compact('account'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateTransactionRequest $request, Account $account): RedirectResponse
    {
        $this->authorize('update', $account);
        
        $data = $request->validated();
        
        // Ensure we always use transaction_date instead of date for consistency
        if (isset($data['date']) && !isset($data['transaction_date'])) {
            $data['transaction_date'] = $data['date'];
            unset($data['date']);
        }
        
        $transaction = new Transaction($data);
        $transaction->account_id = $account->id;
        $transaction->user_id = auth()->id();
        $transaction->save();
        
        // The account balance is automatically updated via the model event listener
        
        // Return to the transactions list for this account instead of the account page
        return redirect()->route('accounts.transactions.index', $account)
            ->with('success', __('transactions.added_successfully'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Transaction $transaction): View
    {
        $this->authorize('view', $transaction);
        
        return view('transactions.show', compact('transaction'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Transaction $transaction): View
    {
        $this->authorize('update', $transaction);
        
        return view('transactions.edit', compact('transaction'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateTransactionRequest $request, Transaction $transaction): RedirectResponse
    {
        $this->authorize('update', $transaction);
        
        $data = $request->validated();
        
        // Ensure we always use transaction_date instead of date for consistency
        if (isset($data['date']) && !isset($data['transaction_date'])) {
            $data['transaction_date'] = $data['date'];
            unset($data['date']);
        }
        
        $transaction->update($data);
        
        // The account balance is automatically updated via the model event listener
        
        return redirect()->route('transactions.show', $transaction)
            ->with('success', __('transactions.updated_successfully'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Transaction $transaction): RedirectResponse
    {
        $this->authorize('delete', $transaction);
        
        $account = $transaction->account;
        $transaction->delete();
        
        // The account balance is automatically updated via the model event listener
        
        return redirect()->route('accounts.show', $account)
            ->with('success', __('transactions.deleted_successfully'));
    }
}
