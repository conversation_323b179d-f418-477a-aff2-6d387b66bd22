<?php

namespace App\Http\Controllers;

use App\Services\DashboardService;
use Illuminate\View\View;

class DashboardController extends Controller
{
    protected DashboardService $dashboardService;
    
    public function __construct(DashboardService $dashboardService)
    {
        $this->dashboardService = $dashboardService;
    }
    
    public function index(): View
    {
        $dashboardData = $this->dashboardService->getDashboardData();
        
        return view('dashboard.index', $dashboardData);
    }
}
