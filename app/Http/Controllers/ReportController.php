<?php

namespace App\Http\Controllers;

use App\Models\Account;
use Illuminate\Http\Request;
// use Barryvdh\DomPDF\Facade\Pdf;
use Omaralalwi\Gpdf\Facades\Gpdf;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\TransactionsExport;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class ReportController extends Controller
{
    /**
     * Export account transactions to Excel
     */
    public function exportExcel(Account $account): BinaryFileResponse
    {
        $this->authorize('view', $account);
        
        $fileName = str_slug($account->name) . '-transactions-' . now()->format('Y-m-d') . '.xlsx';
        
        return Excel::download(new TransactionsExport($account), $fileName);
    }
    
    /**
     * Export account transactions to PDF
     */
    public function exportPdf(Account $account)
    {
        $this->authorize('view', $account);
        
        $transactions = $account->transactions()
            ->with('user')
            ->latest('transaction_date')
            ->get();
        
        $view = view('reports.transactions_pdf', [
            'account' => $account,
            'transactions' => $transactions,
        ])->render();
        
        $fileName = str_slug($account->name) . '-transactions-' . now()->format('Y-m-d');
        
        return Gpdf::stream($view, $fileName . '.pdf');
    }
}
