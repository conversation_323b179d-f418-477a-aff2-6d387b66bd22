<?php

namespace App\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class LanguageController extends Controller
{
    /**
     * Switch the application language.
     *
     * @param string $locale The locale to switch to
     * @return RedirectResponse
     */
    public function switch(string $locale): RedirectResponse
    {
        // Check if the locale is supported
        if (!in_array($locale, ['en', 'ar'])) {
            $locale = 'ar'; // Default to Arabic if unsupported
        }

        // Store the locale in the session
        Session::put('locale', $locale);
        
        // Redirect back to the previous page
        return redirect()->back();
    }
} 