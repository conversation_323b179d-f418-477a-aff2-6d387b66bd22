<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class UserSearchController extends Controller
{
    /**
     * Search for users by mobile number
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function searchByMobile(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'mobile_number' => 'required|string|min:10',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $mobileNumber = $request->input('mobile_number');
        $user = User::findByMobileNumber($mobileNumber);

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => __('users.not_found_or_private'),
            ], 404);
        }

        // Return only basic information needed to identify the user
        return response()->json([
            'success' => true,
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'mobile_number' => $user->mobile_number,
            ],
        ]);
    }
}
