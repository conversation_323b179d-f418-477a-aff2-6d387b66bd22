<?php

namespace App\Http\Controllers;

use App\Models\Family;
use App\Models\FamilyInvitation;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class FamilyInvitationController extends Controller
{
    /**
     * Display a listing of pending invitations for the authenticated user.
     */
    public function index(): View
    {
        $pendingInvitations = Auth::user()->familyInvitations()
            ->where('status', 'pending')
            ->with(['family', 'inviter'])
            ->get();

        return view('families.invitations.index', compact('pendingInvitations'));
    }

    /**
     * Invite a user to a family.
     */
    public function store(Request $request, Family $family): RedirectResponse
    {
        $this->authorize('update', $family);

        $request->validate([
            'user_id' => 'sometimes|exists:users,id',
            'email' => 'required_without:user_id|email|exists:users,email',
            'relationship' => 'nullable|string',
        ]);

        // Get the user to invite
        $userId = $request->input('user_id');
        $user = $userId 
            ? User::findOrFail($userId) 
            : User::where('email', $request->input('email'))->firstOrFail();

        // Add the member
        $result = $family->addMember($user, Auth::user(), $request->input('relationship'));

        if ($result === true) {
            return redirect()->route('families.show', $family)
                ->with('success', __('families.member_added_successfully'));
        }

        return redirect()->route('families.show', $family)
            ->with('success', __('families.invitation_sent_successfully'));
    }

    /**
     * View a specific invitation.
     */
    public function show(string $token): View
    {
        $invitation = FamilyInvitation::where('token', $token)
            ->where('status', 'pending')
            ->with(['family', 'inviter'])
            ->firstOrFail();

        // Mark as expired if past expiry date
        if ($invitation->isExpired()) {
            $invitation->update(['status' => 'expired']);
            return view('families.invitations.expired');
        }

        return view('families.invitations.show', compact('invitation'));
    }

    /**
     * Accept a family invitation.
     */
    public function accept(string $token): RedirectResponse
    {
        $invitation = FamilyInvitation::where('token', $token)
            ->where('status', 'pending')
            ->firstOrFail();

        // Check if invitation has expired
        if ($invitation->isExpired()) {
            $invitation->update(['status' => 'expired']);
            return redirect()->route('dashboard')
                ->with('error', __('families.invitation_expired'));
        }

        // Accept the invitation
        $invitation->accept();

        // Add the user to the family
        $invitation->family->users()->attach($invitation->user_id, [
            'relationship' => $invitation->relationship,
        ]);

        return redirect()->route('families.show', $invitation->family)
            ->with('success', __('families.invitation_accepted'));
    }

    /**
     * Decline a family invitation.
     */
    public function decline(string $token): RedirectResponse
    {
        $invitation = FamilyInvitation::where('token', $token)
            ->where('status', 'pending')
            ->firstOrFail();

        // Check if invitation has expired
        if ($invitation->isExpired()) {
            $invitation->update(['status' => 'expired']);
            return redirect()->route('dashboard')
                ->with('error', __('families.invitation_expired'));
        }

        // Reject the invitation
        $invitation->reject();

        return redirect()->route('dashboard')
            ->with('success', __('families.invitation_declined'));
    }
}
