<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if the user has a locale set in session
        if (Session::has('locale')) {
            // Set the application locale to the one in session
            App::setLocale(Session::get('locale'));
        } else {
            // If no locale is set in session, use the default from config
            App::setLocale(config('app.locale'));
            
            // Store the default locale in session for future requests
            Session::put('locale', config('app.locale'));
        }

        return $next($request);
    }
}
