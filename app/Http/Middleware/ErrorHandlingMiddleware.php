<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class ErrorHandlingMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            return $next($request);
        } catch (Throwable $exception) {
            // Log the exception
            report($exception);
            
            // Return a custom error response
            $statusCode = $this->getStatusCode($exception);
            
            // Set error message in the session
            session()->flash('error', $exception->getMessage());
            
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => $exception->getMessage(),
                    'status_code' => $statusCode
                ], $statusCode);
            }
            
            // Render appropriate view based on the error code
            $view = "errors.{$statusCode}";
            if (!view()->exists($view)) {
                $view = 'errors.500';
            }
            
            return response()->view($view, [
                'exception' => $exception,
            ], $statusCode);
        }
    }
    
    /**
     * Get the appropriate status code for the exception.
     */
    protected function getStatusCode(Throwable $exception): int
    {
        return method_exists($exception, 'getStatusCode') ? $exception->getStatusCode() : 500;
    }
} 