<?php

namespace App\Http\Requests;

use App\Http\Rules\UserRules;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

class UserUpdateRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(Request $request)
    {
        $user = $this->route('user');
        
        // Only the user themselves or an admin can update a user
        return auth()->check() && (auth()->id() === $user->id || auth()->user()->hasRole('admin'));
    }

    public function rules()
    {
        $user = $this->route('user');
        
        $rules = [
            // Make email unique except for the current user
            'email' => 'required|email|max:255|unique:users,email,' . $user->id,
            // Don't require password for profile updates
            'password' => 'nullable|confirmed|min:8',
        ];
        
        return array_merge($rules, UserRules::sharedRules());
    }

    public function attributes()
    {
        $attr = [];
        return array_merge($attr, UserRules::sharedAttributes());
    }

    public function messages()
    {
        $messages = [
            'email.unique' => 'This email address is already in use by another account',
        ];
        
        return array_merge($messages, UserRules::sharedMessages());
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'allow_search_by_mobile' => $this->has('allow_search_by_mobile'),
        ]);
    }
} 