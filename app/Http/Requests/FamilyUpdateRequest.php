<?php

namespace App\Http\Requests;

use App\Http\Rules\FamilyRules;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

class FamilyUpdateRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(Request $request)
    {
        $family = $this->route('family');
        return auth()->check() && auth()->id() === $family->owner_id;
    }

    public function rules()
    {
        $rules = [];
        return array_merge($rules, FamilyRules::sharedRules());
    }

    public function attributes()
    {
        $attr = [];
        return array_merge($attr, FamilyRules::sharedAttributes());
    }

    public function messages()
    {
        $messages = [];
        return array_merge($messages, FamilyRules::sharedMessages());
    }

    protected function prepareForValidation()
    {
        // Don't set owner_id on update
    }
} 