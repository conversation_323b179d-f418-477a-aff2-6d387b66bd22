<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

class FamilyAddMemberRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(Request $request)
    {
        $family = $this->route('family');
        // Only family owner can add members
        return auth()->check() && auth()->id() === $family->owner_id;
    }

    public function rules()
    {
        return [
            'user_id' => 'required|exists:users,id',
            'role' => 'nullable|string|in:member,admin',
        ];
    }

    public function attributes()
    {
        return [
            'user_id' => 'family member',
            'role' => 'member role',
        ];
    }

    public function messages()
    {
        return [
            'user_id.required' => 'Please select a user to add to the family',
            'user_id.exists' => 'The selected user does not exist',
            'role.in' => 'Invalid family member role',
        ];
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'role' => $this->input('role', 'member'),
        ]);
    }
} 