<?php

namespace App\Http\Requests;

use App\Http\Rules\FamilyRules;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

class FamilyCreateRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(Request $request)
    {
        return auth()->check();
    }

    public function rules()
    {
        $rules = [];
        return array_merge($rules, FamilyRules::sharedRules());
    }

    public function attributes()
    {
        $attr = [];
        return array_merge($attr, FamilyRules::sharedAttributes());
    }

    public function messages()
    {
        $messages = [];
        return array_merge($messages, FamilyRules::sharedMessages());
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'owner_id' => auth()->id(),
        ]);
    }
} 