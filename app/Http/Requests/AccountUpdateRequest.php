<?php

namespace App\Http\Requests;

use App\Http\Rules\AccountRules;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

class AccountUpdateRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(Request $request)
    {
        $account = $this->route('account');
        return auth()->check() && auth()->id() === $account->owner_id;
    }

    public function rules()
    {
        $rules = [];
        return array_merge($rules, AccountRules::sharedRules());
    }

    public function attributes()
    {
        $attr = [];
        return array_merge($attr, AccountRules::sharedAttributes());
    }

    public function messages()
    {
        $messages = [];
        return array_merge($messages, AccountRules::sharedMessages());
    }

    protected function prepareForValidation()
    {
        // No need to set owner_id for update
        // We only want to modify provided values
    }
} 