<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

class AccountShareRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(Request $request)
    {
        $account = $this->route('account');
        // Only account owner can share
        return auth()->check() && auth()->id() === $account->owner_id;
    }

    public function rules()
    {
        return [
            'user_id' => 'required|exists:users,id',
            'permission' => 'required|in:read,write,admin',
        ];
    }

    public function attributes()
    {
        return [
            'user_id' => 'user',
            'permission' => 'permission level',
        ];
    }

    public function messages()
    {
        return [
            'user_id.required' => 'Please select a user to share with',
            'user_id.exists' => 'The selected user does not exist',
            'permission.required' => 'Please specify a permission level',
            'permission.in' => 'Invalid permission level',
        ];
    }
} 