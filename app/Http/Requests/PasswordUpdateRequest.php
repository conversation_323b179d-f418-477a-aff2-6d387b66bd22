<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;
use Illuminate\Validation\Rules\Password;

class PasswordUpdateRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(Request $request)
    {
        $user = $this->route('user') ?? auth()->user();
        
        // Only the user themselves or an admin can update a password
        return auth()->check() && (auth()->id() === $user->id || auth()->user()->hasRole('admin'));
    }

    public function rules()
    {
        return [
            'current_password' => ['required', 'current_password'],
            'password' => ['required', 'confirmed', Password::min(8)->letters()->numbers()->symbols()],
            'password_confirmation' => ['required'],
        ];
    }

    public function attributes()
    {
        return [
            'current_password' => 'current password',
            'password' => 'new password',
            'password_confirmation' => 'password confirmation',
        ];
    }

    public function messages()
    {
        return [
            'current_password.required' => 'Please enter your current password',
            'current_password.current_password' => 'The current password is incorrect',
            'password.required' => 'Please enter a new password',
            'password.confirmed' => 'Password confirmation does not match',
            'password_confirmation.required' => 'Please confirm your new password',
        ];
    }
} 