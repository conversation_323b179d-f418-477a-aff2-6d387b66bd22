<?php

namespace App\Http\Requests;

use App\Http\Rules\UserRules;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

class UserCreateRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(Request $request)
    {
        // For user creation, we need to handle different cases:
        // 1. Admin creating a user (needs admin permission)
        // 2. Self-registration (public route, no auth required)
        
        // This will depend on your app's requirements
        // For now, allowing public access for registration
        return true;
    }

    public function rules()
    {
        $rules = [
            // Add the unique constraint for registration
            'email' => 'required|email|max:255|unique:users,email',
            'password' => 'required|min:8|confirmed',
        ];
        
        return array_merge($rules, UserRules::sharedRules());
    }

    public function attributes()
    {
        $attr = [];
        return array_merge($attr, UserRules::sharedAttributes());
    }

    public function messages()
    {
        $messages = [
            'email.unique' => 'This email address is already registered',
        ];
        
        return array_merge($messages, UserRules::sharedMessages());
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'allow_search_by_mobile' => $this->has('allow_search_by_mobile'),
            'family_invitation_mode' => $this->input('family_invitation_mode', 'approval'),
        ]);
    }
} 