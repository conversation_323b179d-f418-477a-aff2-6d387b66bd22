<?php

namespace App\Http\Requests;

use App\Http\Rules\AccountRules;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

class AccountCreateRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(Request $request)
    {
        return auth()->check();
    }

    public function rules()
    {
        $rules = [];
        return array_merge($rules, AccountRules::sharedRules());
    }

    public function attributes()
    {
        $attr = [];
        return array_merge($attr, AccountRules::sharedAttributes());
    }

    public function messages()
    {
        $messages = [];
        return array_merge($messages, AccountRules::sharedMessages());
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'owner_id' => auth()->id(),
        ]);
    }
} 