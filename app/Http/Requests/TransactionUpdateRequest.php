<?php

namespace App\Http\Requests;

use App\Http\Rules\TransactionRules;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

class TransactionUpdateRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(Request $request)
    {
        $transaction = $this->route('transaction');
        
        if (!auth()->check()) {
            return false;
        }
        
        // Only transaction creator or account owner can update
        return auth()->id() === $transaction->user_id || 
               auth()->id() === $transaction->account->owner_id;
    }

    public function rules()
    {
        $rules = [
            // Remove account_id requirement for updates
            'account_id' => 'sometimes|exists:accounts,id',
        ];
        
        return array_merge($rules, TransactionRules::sharedRules());
    }

    public function attributes()
    {
        $attr = [];
        return array_merge($attr, TransactionRules::sharedAttributes());
    }

    public function messages()
    {
        $messages = [];
        return array_merge($messages, TransactionRules::sharedMessages());
    }

    protected function prepareForValidation()
    {
        // We don't want to modify user_id on updates
    }
} 