<?php

namespace App\Http\Requests;

use App\Http\Rules\TransactionRules;
use App\Models\Account;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Request;

class TransactionCreateRequest extends FormRequest
{
    /**
     * @return bool
     */
    public function authorize(Request $request)
    {
        if (!auth()->check()) {
            return false;
        }

        // Check if user has access to the account
        $account = Account::findOrFail($this->input('account_id'));
        return $account->owner_id === auth()->id() ||
               $account->users()->where('user_id', auth()->id())->exists();
    }

    public function rules()
    {
        $rules = [];
        return array_merge($rules, TransactionRules::sharedRules());
    }

    public function attributes()
    {
        $attr = [];
        return array_merge($attr, TransactionRules::sharedAttributes());
    }

    public function messages()
    {
        $messages = [];
        return array_merge($messages, TransactionRules::sharedMessages());
    }

    protected function prepareForValidation()
    {
        $this->merge([
            'user_id' => auth()->id(),
        ]);
    }
}
