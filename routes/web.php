<?php

use App\Http\Controllers\AccountController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\FamilyController;
use App\Http\Controllers\FamilyInvitationController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\UserSearchController;
use Illuminate\Support\Facades\Route;

// Redirect the home page to dashboard for authenticated users
Route::get('/', function () {
    if (auth()->check()) {
        return redirect()->route('dashboard');
    }
    return view('welcome');
});

// Language switch route
Route::get('/language/{locale}', [LanguageController::class, 'switch'])->name('language.switch');

// Family invitation public routes (accessible without login)
Route::get('/families/invitations/{token}', [FamilyInvitationController::class, 'show'])->name('families.invitations.show');
Route::post('/families/invitations/{token}/accept', [FamilyInvitationController::class, 'accept'])->name('families.invitations.accept');
Route::post('/families/invitations/{token}/decline', [FamilyInvitationController::class, 'decline'])->name('families.invitations.decline');

// Dashboard & authenticated routes
Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // Accounts
    Route::resource('accounts', AccountController::class);
    
    // Families
    Route::resource('families', FamilyController::class);
    Route::get('/families/{family}/add-member', [FamilyController::class, 'addMemberForm'])->name('families.add-member.form');
    Route::post('/families/{family}/add-member', [FamilyController::class, 'addMember'])->name('families.add-member');
    Route::delete('/families/{family}/members/{user}', [FamilyController::class, 'removeMember'])->name('families.remove-member');
    Route::get('/families/{family}/share-account', [FamilyController::class, 'shareAccountForm'])->name('families.share-account.form');
    Route::post('/families/{family}/share-account', [FamilyController::class, 'shareAccount'])->name('families.share-account');
    Route::delete('/families/{family}/accounts/{account}', [FamilyController::class, 'unshareAccount'])->name('families.unshare-account');
    
    // Family Invitations
    Route::get('/invitations', [FamilyInvitationController::class, 'index'])->name('invitations.index');
    Route::post('/families/{family}/invitations', [FamilyInvitationController::class, 'store'])->name('families.invitations.store');
    
    // User Search
    Route::post('/users/search/mobile', [UserSearchController::class, 'searchByMobile'])->name('users.search.mobile');
    
    // Transactions
    Route::resource('accounts.transactions', TransactionController::class)->shallow();
    
    // Reports
    Route::get('/reports/accounts/{account}/excel', [ReportController::class, 'exportExcel'])->name('reports.accounts.excel');
    Route::get('/reports/accounts/{account}/pdf', [ReportController::class, 'exportPdf'])->name('reports.accounts.pdf');
    
    // Profile routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::patch('/profile/privacy', [ProfileController::class, 'updatePrivacy'])->name('profile.privacy.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
