<?php

use App\Models\Account;
use App\Models\Family;
use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

// User private channel
Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

// Account channel for real-time notifications
Broadcast::channel('account.{accountId}', function ($user, $accountId) {
    $account = Account::findOrFail($accountId);
    
    // Check if user is owner or has access to this account
    return $account->owner_id === $user->id || $account->users()->where('user_id', $user->id)->exists();
});

// Family channel for members
Broadcast::channel('family.{familyId}', function ($user, $familyId) {
    $family = Family::findOrFail($familyId);
    
    // Check if user is owner or member of this family
    return $family->owner_id === $user->id || $family->users()->where('user_id', $user->id)->exists();
}); 